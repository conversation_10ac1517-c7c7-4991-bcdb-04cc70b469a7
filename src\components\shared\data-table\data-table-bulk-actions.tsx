"use client";

import * as React from "react";
import { Table } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";

interface BulkAction {
  key: string;
  label: string;
  icon?: React.ReactNode;
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  onClick: (selectedRows: any[]) => void;
}

interface DataTableBulkActionsProps<TData> {
  table: Table<TData>;
  bulkActions?: BulkAction[];
}

export function DataTableBulkActions<TData>({
  table,
  bulkActions = [],
}: DataTableBulkActionsProps<TData>) {
  const selectedRows = table.getFilteredSelectedRowModel().rows;
  const selectedCount = selectedRows.length;

  if (selectedCount === 0) {
    return null;
  }

  const handleClearSelection = () => {
    table.resetRowSelection();
  };

  const getSelectedData = () => {
    return selectedRows.map((row) => row.original);
  };

  return (
    <div className="flex flex-col rounded-md border bg-muted/50 px-4 py-2 gap-4">
      <div className="flex flex-wrap items-center gap-2 mt-2">
        {bulkActions.map((action) => (
          <Button
            key={action.key}
            variant={action.variant || "default"}
            size="sm"
            onClick={() => action.onClick(getSelectedData())}
            className="h-8"
          >
            {action.icon && <span className="mr-1">{action.icon}</span>}
            {action.label}
          </Button>
        ))}
      </div>

      <div className="flex items-center gap-2">
        <span className="text-sm font-medium">
          {selectedCount} of {table.getFilteredRowModel().rows.length} row(s)
          selected
        </span>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClearSelection}
          className="h-6 w-6 p-0"
        >
          <X className="h-3 w-3" />
          <span className="sr-only">Clear selection</span>
        </Button>
      </div>
    </div>
  );
}
