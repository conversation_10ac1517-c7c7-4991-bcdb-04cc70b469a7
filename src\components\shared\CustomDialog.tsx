"use client";

import { Dialog } from "@radix-ui/react-dialog";
import {
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  Di<PERSON><PERSON>eader,
  Di<PERSON><PERSON>itle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "../ui/button";
import { useState } from "react";
import { Loader } from "lucide-react";
import { cn } from "@/lib/utils";

interface Props {
  title?: string;
  description?: string;
  trigger: React.ReactNode;
  children?: React.ReactNode;
  asChild?: boolean;
  footer?: boolean;
  onConfirm?: () => Promise<void> | void;
}

const CustomDialog = ({
  title = "Are you sure?",
  description,
  trigger,
  children,
  asChild = true,
  footer = false,
  onConfirm,
}: Props) => {
  const [loading, setLoading] = useState(false);

  const handleClick = async () => {
    if (!onConfirm) return;
    try {
      setLoading(true);
      await onConfirm();
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog>
      <DialogTrigger
        asChild={asChild}
        className={cn(
          !asChild
            ? "hover:text-primary cmw-dropdown-child w-full text-start px-2 py-1 text-[14px] hover:bg-muted rounded-sm"
            : ""
        )}
      >
        {trigger}
      </DialogTrigger>
      <DialogContent className="max-h-[70%] overflow-y-auto no-scrollbar w-7xl">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>
        {children}
        {footer && (
          <DialogFooter>
            <Button onClick={handleClick} disabled={loading}>
              {loading ? <Loader className="animate-spin" /> : "Confirm"}
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CustomDialog;
