import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Actions, Resources } from "@/lib/permissions";
import { Plus } from "lucide-react";
import PageWrapper from "../../_components/layouts/PageWrapper";
import DepartmentTable from "./table";

const breadcrumbItems = [
  { label: "Home", href: "/" },
  { label: "Departments" },
];

export default function Departments() {
  const renderButton = () => {
    return (
      <ClientRoleGuard resource={Resources.DEPARTMENTS} action={Actions.CREATE}>
        <Button>
          <Plus /> Add Department
        </Button>
      </ClientRoleGuard>
    );
  };

  return (
    <PageWrapper
      pgTitle="Manage Departments"
      pgDescription="Manage department records and information"
      breadcrumbItems={breadcrumbItems}
      headerButton={renderButton()}
    >
      <DepartmentTable />
    </PageWrapper>
  );
}
