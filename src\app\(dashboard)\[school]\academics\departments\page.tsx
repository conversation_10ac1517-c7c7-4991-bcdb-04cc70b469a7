"use client";

import { DataTable } from "@/components/shared/table/data-table";

const mockStudents = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    grade: "Grade 10",
    status: "Active",
    enrollmentDate: "2024-01-15",
    gpa: 3.8,
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    grade: "Grade 11",
    status: "Active",
    enrollmentDate: "2024-01-10",
    gpa: 3.9,
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    grade: "Grade 9",
    status: "Inactive",
    enrollmentDate: "2024-02-01",
    gpa: 3.5,
  },
  {
    id: 4,
    name: "<PERSON>",
    email: "<EMAIL>",
    grade: "Grade 12",
    status: "Active",
    enrollmentDate: "2024-01-20",
    gpa: 4.0,
  },
  {
    id: 5,
    name: "<PERSON>",
    email: "d<PERSON>wils<PERSON>@school.com",
    grade: "Grade 10",
    status: "Active",
    enrollmentDate: "2024-01-25",
    gpa: 3.7,
  },
  {
    id: 6,
    name: "<PERSON>",
    email: "<EMAIL>",
    grade: "Grade 11",
    status: "Suspended",
    enrollmentDate: "2024-01-12",
    gpa: 3.2,
  },
  {
    id: 7,
    name: "<PERSON>",
    email: "<EMAIL>",
    grade: "Grade 9",
    status: "Active",
    enrollmentDate: "2024-02-05",
    gpa: 3.6,
  },
  {
    id: 8,
    name: "Lisa Anderson",
    email: "<EMAIL>",
    grade: "Grade 12",
    status: "Active",
    enrollmentDate: "2024-01-08",
    gpa: 3.95,
  },
];

const fetchStudents = async (params: any) => {
  await new Promise((resolve) => setTimeout(resolve, 500));

  let filteredData = [...mockStudents];

  if (params.search) {
    filteredData = filteredData.filter(
      (item) =>
        item.name.toLowerCase().includes(params.search.toLowerCase()) ||
        item.email.toLowerCase().includes(params.search.toLowerCase()) ||
        item.grade.toLowerCase().includes(params.search.toLowerCase())
    );
  }

  if (params.filters.grade) {
    filteredData = filteredData.filter(
      (item) => item.grade === params.filters.grade
    );
  }
  if (params.filters.status) {
    filteredData = filteredData.filter(
      (item) => item.status === params.filters.status
    );
  }

  if (params.sortBy && params.sortOrder) {
    filteredData.sort((a, b) => {
      let aValue = a[params.sortBy];
      let bValue = b[params.sortBy];

      if (params.sortBy === "enrollmentDate") {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }

      if (params.sortOrder === "asc") {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
  }

  const total = filteredData.length;
  const startIndex = (params.page - 1) * params.pageSize;
  const endIndex = startIndex + params.pageSize;
  const paginatedData = filteredData.slice(startIndex, endIndex);

  return {
    data: paginatedData,
    total,
    page: params.page,
    totalPages: Math.ceil(total / params.pageSize),
    hasNextPage: params.page < Math.ceil(total / params.pageSize),
    hasPreviousPage: params.page > 1,
  };
};

export default function SchoolManagementTable() {
  const columns = [
    { key: "name", label: "Name", sortable: true },
    { key: "email", label: "Email" },
    { key: "grade", label: "Grade", sortable: true },
    { key: "status", label: "Status" },
    { key: "gpa", label: "GPA", sortable: true },
    {
      key: "enrollmentDate",
      label: "Enrollment Date",
      sortable: true,
      type: "date",
    },
  ];

  const filterConfig = [
    {
      key: "grade",
      label: "Grade",
      placeholder: "All Grades",
      options: [
        { value: "Grade 9", label: "Grade 9" },
        { value: "Grade 10", label: "Grade 10" },
        { value: "Grade 11", label: "Grade 11" },
        { value: "Grade 12", label: "Grade 12" },
      ],
    },
    {
      key: "status",
      label: "Status",
      placeholder: "All Statuses",
      options: [
        { value: "Active", label: "Active" },
        { value: "Inactive", label: "Inactive" },
        { value: "Suspended", label: "Suspended" },
      ],
    },
  ];

  const handleRowAction = (action: string, row: any) => {
    console.log(`${action} action for:`, row);
    switch (action) {
      case "view":
        alert(`Viewing details for ${row.name}`);
        break;
      case "edit":
        alert(`Editing ${row.name}`);
        break;
      case "delete":
        if (confirm(`Are you sure you want to delete ${row.name}?`)) {
          console.log("Deleting:", row);
        }
        break;
    }
  };

  const handleBulkAction = (action: string, selectedIds: number[]) => {
    console.log(`${action} action for IDs:`, selectedIds);
    switch (action) {
      case "bulk-edit":
        alert(`Bulk editing ${selectedIds.length} students`);
        break;
      case "bulk-delete":
        if (
          confirm(
            `Are you sure you want to delete ${selectedIds.length} students?`
          )
        ) {
          console.log("Bulk deleting:", selectedIds);
        }
        break;
    }
  };

  const customHeaderActions = [
    {
      key: "add-student",
      label: "Add Student",
      variant: "primary",
      onClick: () => alert("Add new student"),
    },
  ];

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-7xl mx-auto">
        <DataTable
          fetchData={fetchStudents}
          title="Student Management"
          description="View and manage student records in your school system"
          columns={columns}
          filterConfig={filterConfig}
          onRowAction={handleRowAction}
          onBulkAction={handleBulkAction}
          headerActions={customHeaderActions}
          searchPlaceholder="Search students by name, email, or grade..."
          emptyMessage="No students found"
          enableSearch={true}
          enableFilters={true}
          enableBulkActions={true}
          enableExport={true}
          initialPageSize={10}
          pageSizeOptions={[5, 10, 15, 25, 50]}
        />
      </div>
    </div>
  );
}
