"use client";

import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTableColumnHeader } from "@/components/shared/data-table/data-table-column-header";
import type { Staff_Department } from "@/lib/types";
import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import { Actions, Resources } from "@/lib/permissions";
import { CustomSheet } from "@/components/shared/CustomSheet";
import CustomAlertDialog from "@/components/shared/CustomAlertDialog";

export const columns: ColumnDef<Staff_Department>[] = [
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="NAME" />
    ),
  },
  {
    accessorKey: "alias",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="ALIAS" />
    ),
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="CREATED AT" />
    ),
    cell: ({ row }) => {
      const createdAt = row.getValue("createdAt") as Date;
      return createdAt.toLocaleDateString();
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const department = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="data-[state=open]:bg-muted size-8"
            >
              <MoreHorizontal />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[160px]">
            <DropdownMenuItem>View</DropdownMenuItem>
            <ClientRoleGuard
              resource={Resources.STAFF_DEPARTMENTS}
              action={Actions.MANAGE}
            >
              <DropdownMenuItem asChild>
                <CustomSheet
                  title="Edit Department"
                  className="w-full"
                  asChild={false}
                  trigger={<span>Edit</span>}
                >
                  <div>Edit Department Form</div>
                </CustomSheet>
              </DropdownMenuItem>
              <DropdownMenuItem>Duplicate</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <CustomAlertDialog
                  title="Delete Department"
                  description="Are you sure you want to delete this department? This action cannot be undone."
                  onConfirm={() => console.log("Delete clicked")}
                  className="w-full"
                  asChild={false}
                  trigger={<span>Delete</span>}
                />
                {/* <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut> */}
              </DropdownMenuItem>
            </ClientRoleGuard>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
