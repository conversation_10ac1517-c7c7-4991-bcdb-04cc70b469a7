export type UserRole = "admin" | "teacher" | "student" | "parent" | "applicant"

export interface User {
  id: string
  email: string
  name: string
  role: UserRole
  avatar?: string
  createdAt: Date
  updatedAt: Date
}

export interface Admin extends User {
  role: "admin"
  permissions: string[]
}

export interface Teacher extends User {
  role: "teacher"
  employeeId: string
  subjects: string[]
  classes: string[]
  department: string
}

export interface Student extends User {
  role: "student"
  studentId: string
  grade: string
  class: string
  parentId: string
  dateOfBirth: Date
}

export interface Parent extends User {
  role: "parent"
  children: string[] // student IDs
  phone: string
}

export interface Class {
  id: string
  name: string
  grade: string
  teacherId: string
  students: string[]
  subjects: string[]
}

export interface Subject {
  id: string
  name: string
  code: string
  teacherId: string
  classes: string[]
}

export interface Assignment {
  id: string
  title: string
  description: string
  subjectId: string
  classId: string
  teacherId: string
  dueDate: Date
  createdAt: Date
}

export interface Grade {
  id: string
  studentId: string
  assignmentId: string
  score: number
  maxScore: number
  feedback?: string
  gradedAt: Date
}

export interface Attendance {
  id: string
  studentId: string
  classId: string
  date: Date
  status: "present" | "absent" | "late"
  notes?: string
}

export interface Role {
  id: string;
  name: string;
  displayName: string;
  description: string;
  permissions: string[];
  isSystemRole: boolean; // Cannot be deleted
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Permission system
export interface Permission {
  id: string
  name: string
  description: string
  resource: string
  action: string
}

export interface RolePermissions {
  [key: string]: Permission[]
}

export interface Route {
  path: string
  name: string
  icon: React.ReactNode
  roles: UserRole[]
}

export interface MenuItem {
  label: string
  icon: React.ReactNode
  href: string
}

export interface SidebarItem {
  label: string
  icon: React.ReactNode
  href: string
  roles: UserRole[]
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
}

export interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
  hasPermission: (resource: string, action: string) => boolean
}
