"use client";

import { useState } from "react";
import type { UseFormReturn } from "react-hook-form";
import { Check, X, ChevronsUpDown } from "lucide-react";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface MultiSelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface FormMultiSelectProps {
  form: UseFormReturn<any>;
  name: string;
  label?: string;
  placeholder?: string;
  description?: string;
  options: MultiSelectOption[];
  disabled?: boolean;
  className?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  maxSelected?: number;
}

export function FormMultiSelect({
  form,
  name,
  label,
  placeholder = "Select options...",
  description,
  options,
  disabled,
  className,
  searchPlaceholder = "Search options...",
  emptyMessage = "No option found.",
  maxSelected,
}: FormMultiSelectProps) {
  const [open, setOpen] = useState(false);

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => {
        const selectedValues = field.value || [];

        return (
          <FormItem className={className}>
            {label && <FormLabel>{label}</FormLabel>}
            <div className="space-y-2">
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={open}
                      className={cn(
                        "w-full justify-between",
                        selectedValues.length === 0 && "text-muted-foreground"
                      )}
                      disabled={disabled}
                    >
                      {selectedValues.length === 0
                        ? placeholder
                        : `${selectedValues.length} selected`}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandInput placeholder={searchPlaceholder} />
                    <CommandList>
                      <CommandEmpty>{emptyMessage}</CommandEmpty>
                      <CommandGroup>
                        {options.map((option) => {
                          const isSelected = selectedValues.includes(
                            option.value
                          );
                          const isDisabled =
                            option.disabled ||
                            (!isSelected &&
                              selectedValues.length >= maxSelected!);

                          return (
                            <CommandItem
                              key={option.value}
                              value={option.value}
                              disabled={isDisabled}
                              onSelect={() => {
                                if (isSelected) {
                                  field.onChange(
                                    selectedValues.filter(
                                      (value: string) => value !== option.value
                                    )
                                  );
                                } else {
                                  field.onChange([
                                    ...selectedValues,
                                    option.value,
                                  ]);
                                }
                              }}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  isSelected ? "opacity-100" : "opacity-0"
                                )}
                              />
                              {option.label}
                            </CommandItem>
                          );
                        })}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>

              {selectedValues.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {selectedValues.map((value: string) => {
                    const option = options.find((opt) => opt.value === value);
                    return (
                      <Badge
                        key={value}
                        variant="secondary"
                        className="text-xs"
                      >
                        {option?.label}
                        <button
                          type="button"
                          className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              field.onChange(
                                selectedValues.filter(
                                  (v: string) => v !== value
                                )
                              );
                            }
                          }}
                          onMouseDown={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                          }}
                          onClick={() =>
                            field.onChange(
                              selectedValues.filter((v: string) => v !== value)
                            )
                          }
                          disabled={disabled}
                        >
                          <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                        </button>
                      </Badge>
                    );
                  })}
                </div>
              )}
            </div>
            {description && <FormDescription>{description}</FormDescription>}
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
