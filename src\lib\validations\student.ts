import { z } from "zod";

export const studentSchema = z.object({
  id: z.string().optional(),
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.email("Invalid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  dateOfBirth: z.date({
    error: "Date of birth is required",
  }),
  gender: z.enum(["male", "female", "other"], {
    error: "Please select a gender",
  }),
  grade: z.string().min(1, "Please select a grade"),
  subjects: z.array(z.string()).min(1, "Please select at least one subject"),
  address: z.string().min(10, "Address must be at least 10 characters"),
  emergencyContact: z
    .string()
    .min(10, "Emergency contact must be at least 10 digits"),
  hasScholarship: z.boolean(),
  scholarshipType: z.string().optional(),
  notes: z.string().optional(),
  profilePicture: z.string().optional(),
});

export type StudentFormData = z.infer<typeof studentSchema>;

// Sample data for demonstration
export const gradeOptions = [
  { value: "kindergarten", label: "Kindergarten" },
  { value: "grade-1", label: "Grade 1" },
  { value: "grade-2", label: "Grade 2" },
  { value: "grade-3", label: "Grade 3" },
  { value: "grade-4", label: "Grade 4" },
  { value: "grade-5", label: "Grade 5" },
  { value: "grade-6", label: "Grade 6" },
  { value: "grade-7", label: "Grade 7" },
  { value: "grade-8", label: "Grade 8" },
  { value: "grade-9", label: "Grade 9" },
  { value: "grade-10", label: "Grade 10" },
  { value: "grade-11", label: "Grade 11" },
  { value: "grade-12", label: "Grade 12" },
];

export const subjectOptions = [
  { value: "mathematics", label: "Mathematics" },
  { value: "english", label: "English" },
  { value: "science", label: "Science" },
  { value: "history", label: "History" },
  { value: "geography", label: "Geography" },
  { value: "physics", label: "Physics" },
  { value: "chemistry", label: "Chemistry" },
  { value: "biology", label: "Biology" },
  { value: "art", label: "Art" },
  { value: "music", label: "Music" },
  { value: "physical-education", label: "Physical Education" },
  { value: "computer-science", label: "Computer Science" },
];

export const scholarshipOptions = [
  { value: "academic", label: "Academic Excellence" },
  { value: "sports", label: "Sports Scholarship" },
  { value: "need-based", label: "Need-Based" },
  { value: "merit", label: "Merit Scholarship" },
];

export const genderOptions = [
  { value: "male", label: "Male" },
  { value: "female", label: "Female" },
  { value: "other", label: "Other" },
];
