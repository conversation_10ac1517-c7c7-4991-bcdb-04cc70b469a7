"use client";

import { DataTable } from "@/components/shared/data-table/data-table";
// import { getStaffs, Staffs } from "@/lib/server/staffs/staffs.list";
import {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import React, { useEffect, useState } from "react";
import { columns } from "./columns";
import { filterConfigs } from "./filtering";
import { staffBulkActions } from "./bulk-actions";
import { getStaffs } from "@/lib/mock-data";
import { Staff } from "@/lib/types";

export default function StaffTable() {
  const [data, setData] = useState<Staff[]>([]);
  const [loading, setLoading] = useState(true);
  const [pageCount, setPageCount] = useState(-1);

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  async function fetchStaffs(params: {
    pagination: PaginationState;
    sorting: SortingState;
    columnFilters: ColumnFiltersState;
  }) {
    const result = await getStaffs(params);

    return {
      data: result.data,
      pageCount: result.total,
      total: result.total,
    };
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const result = await fetchStaffs({
          pagination,
          sorting,
          columnFilters,
        });
        setData(result.data);
        setPageCount(result.pageCount);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [pagination, sorting, columnFilters]);

  return (
    <>
      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        pageCount={pageCount}
        onPaginationChange={setPagination}
        onSortingChange={setSorting}
        onColumnFiltersChange={setColumnFilters}
        manualPagination={true}
        manualSorting={true}
        manualFiltering={true}
        filterConfigs={filterConfigs}
        searchColumn="name"
        searchPlaceholder="Search staffs by name..."
        enableBulkActions={true}
        bulkActions={staffBulkActions}
      />
    </>
  );
}
