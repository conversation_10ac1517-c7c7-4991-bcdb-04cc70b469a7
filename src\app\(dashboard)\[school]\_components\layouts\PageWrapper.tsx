import { BreadcrumbResponsive } from "@/components/shared/breadcrumb";
import { cn } from "@/lib/utils";

type Items = {
  href?: string;
  label: string;
}[];

type Props = {
  pgTitle: string;
  pgDescription?: string;
  pgHeading?: string;
  breadcrumbItems: Items;
  headerButton?: React.ReactNode;
  children: React.ReactNode;
};

const PageWrapper = ({
  pgTitle,
  pgDescription,
  breadcrumbItems,
  headerButton,
  children,
}: Props) => {
  return (
    <main className="flex flex-col gap-4">
      <BreadcrumbResponsive items={breadcrumbItems} />
      <div className="mb-6 flex justify-between items-center w-full">
        <div>
          <h1 className="text-2xl font-bold">{pgTitle}</h1>
          {pgDescription ? (
            <p className="text-muted-foreground">{pgDescription}</p>
          ) : null}
        </div>
        <>{headerButton}</>
      </div>

      {children}
    </main>
  );
};

export default PageWrapper;
