// src/lib/auth/role-manager.ts
import { Role, Permission, BASE_PERMISSIONS, SYSTEM_ROLES } from './permissions';

class RoleManager {
  private roles: Map<string, Role> = new Map();
  private permissions: Map<string, Permission> = new Map();

  constructor() {
    // Initialize with base permissions
    BASE_PERMISSIONS.forEach(permission => {
      this.permissions.set(permission.id, permission);
    });
    
    // Initialize with system roles (loaded from API in real app)
    SYSTEM_ROLES.forEach(role => {
      this.roles.set(role.id, role);
    });
  }

  // Get all roles
  getRoles(): Role[] {
    return Array.from(this.roles.values()).filter(role => role.isActive);
  }

  // Get role by ID
  getRole(roleId: string): Role | undefined {
    return this.roles.get(roleId);
  }

  // Create new role
  createRole(roleData: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>): Role {
    const newRole: Role = {
      ...roleData,
      id: `custom_${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    this.roles.set(newRole.id, newRole);
    return newRole;
  }

  // Update role permissions
  updateRolePermissions(roleId: string, permissions: string[]): Role | null {
    const role = this.roles.get(roleId);
    if (!role || role.isSystemRole) {
      return null; // Cannot modify system roles
    }

    const updatedRole = {
      ...role,
      permissions,
      updatedAt: new Date().toISOString(),
    };

    this.roles.set(roleId, updatedRole);
    return updatedRole;
  }

  // Check if role has permission
  hasPermission(roleId: string, permissionId: string): boolean {
    const role = this.roles.get(roleId);
    if (!role || !role.isActive) return false;
    
    return role.permissions.includes(permissionId);
  }

  // Get available permissions
  getPermissions(): Permission[] {
    return Array.from(this.permissions.values());
  }

  // Get permissions by category
  getPermissionsByResource(): Record<string, Permission[]> {
    const permissions = this.getPermissions();
    return permissions.reduce((acc, permission) => {
      if (!acc[permission.resource]) {
        acc[permission.resource] = [];
      }
      acc[permission.resource].push(permission);
      return acc;
    }, {} as Record<string, Permission[]>);
  }
}

export const roleManager = new RoleManager();