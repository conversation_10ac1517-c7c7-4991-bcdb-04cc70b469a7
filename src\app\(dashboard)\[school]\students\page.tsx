import { Button } from "@/components/ui/button";
import StudentTable from "./table";
import { Plus } from "lucide-react";
import PageWrapper from "../_components/layouts/PageWrapper";
import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import { Actions, Resources } from "@/lib/permissions";

const breadcrumbItems = [{ label: "Home", href: "/" }, { label: "Students" }];

export default function StudentsPage() {
  const renderButton = () => {
    return (
      <ClientRoleGuard resource={Resources.STUDENTS} action={Actions.CREATE}>
        <Button>
          <Plus /> Add Student
        </Button>
      </ClientRoleGuard>
    );
  };

  return (
    <PageWrapper
      pgTitle="Manage Students"
      pgDescription="Manage student records and information"
      breadcrumbItems={breadcrumbItems}
      headerButton={renderButton()}
    >
      <StudentTable />
    </PageWrapper>
  );
}
