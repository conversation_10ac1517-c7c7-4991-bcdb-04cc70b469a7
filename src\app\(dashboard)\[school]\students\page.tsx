import { Button } from "@/components/ui/button";
import StudentTable from "./table";
import { Plus } from "lucide-react";

export default function StudentsPage() {
  return (
    <main>
      <div className="mb-6 flex justify-between items-center w-full">
        <div>
          <h1 className="text-2xl font-bold">Manage Students</h1>
          <p className="text-muted-foreground">
            This example shows how to configure multiple dynamic filters for any
            columns.
          </p>
        </div>
        <Button>
          <Plus /> Add Student
        </Button>
      </div>

      <StudentTable />
    </main>
  );
}
