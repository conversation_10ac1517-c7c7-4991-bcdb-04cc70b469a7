import { GraduationCap, Shield, User } from "lucide-react";

export const filterConfigs = [
  {
    columnId: "status",
    title: "Status",
    options: [
      { label: "Active", value: "active", icon: User },
      { label: "Inactive", value: "inactive" },
      { label: "Suspended", value: "suspended", icon: Shield },
      { label: "Graduated", value: "graduated", icon: GraduationCap },
    ],
  },
  {
    columnId: "grade",
    title: "Grade",
    options: [
      { label: "A", value: "A" },
      { label: "B", value: "B" },
      { label: "C", value: "C" },
      { label: "D", value: "D" },
      { label: "F", value: "F" },
    ],
  },
  {
    columnId: "department",
    title: "Department",
    options: [
      { label: "Computer Science", value: "CS" },
      { label: "Mathematics", value: "MATH" },
      { label: "Physics", value: "PHYS" },
      { label: "Chemistry", value: "CHEM" },
      { label: "Biology", value: "BIO" },
      { label: "English", value: "ENG" },
    ],
  },
  {
    columnId: "enrollmentType",
    title: "Enrollment",
    options: [
      { label: "Full-time", value: "fulltime" },
      { label: "Part-time", value: "parttime" },
      { label: "Online", value: "online" },
      { label: "Hybrid", value: "hybrid" },
    ],
  },
  {
    columnId: "year",
    title: "Year",
    options: [
      { label: "Freshman", value: "1" },
      { label: "Sophomore", value: "2" },
      { label: "Junior", value: "3" },
      { label: "Senior", value: "4" },
      { label: "Graduate", value: "5" },
    ],
  },
];
