/* eslint-disable @typescript-eslint/no-unused-vars */
import { getUserByEmail } from "@/lib/mock-data"
import { permissions, UserRole } from "@/lib/types"
import NextAuth, { User } from "next-auth"
import Credentials from "next-auth/providers/credentials"
 
export const { handlers, signIn, signOut, auth } = NextAuth({
  session: {
    strategy: 'jwt',
  },
  providers: [
    Credentials({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      authorize: async (credentials, req) => {
        if (!credentials.email || !credentials.password)  return null
        const { email, password } = credentials as { email: string; password: string }

        await new Promise((resolve) => setTimeout(resolve, 500))
        
          // Simple mock authentication - in real app, this would validate against backend
          const user = getUserByEmail(email)
          if (user && password === "password123") {
            return user
          }
          return null
      },
    })
  ],
  pages: {
    signIn: '/login'
  },
  callbacks: {
    jwt: async ({ token, user }) => {
      if (user) {
        token.id = user.id
        token.name = user.name
        token.email = user.email
        token.role = user.role
        token.permissions = user.permissions
        token.school = user.school
        token.avatar = user.avatar
      }
      return token
    },
    session: async ({ session, token }) => {
      if (token) {
        session.user.id = token.id as string
        session.user.name = token.name as string
        session.user.email = token.email as string
        session.user.role = token.role as UserRole
        session.user.permissions = token.permissions as permissions[]
        session.user.school = token.school as string
        session.user.avatar = token.avatar as string | undefined
      }
      return session
    },
  },
})

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      email: string
      name: string
      role: UserRole
      permissions: permissions[]
      school: string
      avatar?: string
    }
  }
  interface User {
    id: string
    email: string
    name: string
    role: UserRole
    permissions: permissions[]
    school: string
    avatar?: string
  }
  interface JWT {
    id: string
    email: string
    name: string
    role: UserRole
    permissions: permissions[]
    school: string
    avatar?: string
  }
}