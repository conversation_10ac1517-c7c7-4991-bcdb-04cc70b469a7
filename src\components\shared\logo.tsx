"use client";

import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

const Logo = () => {
  // const [siteData, setSiteData] = useState<SiteData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // useEffect(() => {
  //   const fetchSiteData = async () => {
  //     const data = await getSiteData();
  //     setSiteData(data);
  //     setIsLoading(false);
  //   };

  //   fetchSiteData();
  // }, []);

  if (isLoading) {
    return <p className="text-sm animate-pulse">Loading...</p>;
  }

  // const image = siteData ? siteData.siteLogo : "/images/logo.png";
  // const name = siteData ? siteData.siteName : "Complexus Pathways";

  return (
    <Link href={"/"} className="flex items-center space-x-1 shrink-0">
      <div className="rounded-full p-1 shrink-0">
        <Image
          src={"/images/placeholder.svg"}
          alt="Logo"
          width={30}
          height={30}
          loading="eager"
        />
      </div>
      <span className="font-semibold text-xl tracking-tighter">
        Kingdom <span className="text-primary">SIS</span>
      </span>
    </Link>
  );
};

export default Logo;
