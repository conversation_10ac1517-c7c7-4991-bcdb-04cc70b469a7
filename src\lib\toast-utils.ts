import { toast } from "sonner";

// Utility functions for consistent toast messaging across the app
export const showSuccessToast = (title: string, description?: string) => {
  toast.success(title, { description });
};

export const showErrorToast = (title: string, description?: string) => {
  toast.error(title, { description });
};

export const showInfoToast = (title: string, description?: string) => {
  toast.info(title, { description });
};

export const showWarningToast = (title: string, description?: string) => {
  toast.warning(title, { description });
};

export const showLoadingToast = (title: string, description?: string) => {
  return toast.loading(title, { description });
};

// Form-specific toast messages
export const formToasts = {
  success: {
    create: (name: string) =>
      showSuccessToast(
        "Record created",
        `${name} has been successfully added.`,
      ),
    update: (name: string) =>
      showSuccessToast(
        "Record updated",
        `${name} has been successfully updated.`,
      ),
    delete: (name: string) =>
      showSuccessToast("Record deleted", `${name} has been removed.`),
  },
  error: {
    create: () =>
      showErrorToast(
        "Creation failed",
        "Unable to create the record. Please try again.",
      ),
    update: () =>
      showErrorToast(
        "Update failed",
        "Unable to update the record. Please try again.",
      ),
    delete: () =>
      showErrorToast(
        "Deletion failed",
        "Unable to delete the record. Please try again.",
      ),
    validation: () =>
      showErrorToast(
        "Validation error",
        "Please check your input and try again.",
      ),
  },
  info: {
    editing: (name: string) =>
      showInfoToast("Editing record", `You are now editing ${name}.`),
    adding: () =>
      showInfoToast(
        "Adding new record",
        "Fill out the form to add a new record.",
      ),
    cancelled: () =>
      showInfoToast("Action cancelled", "No changes were saved."),
    reset: () => showInfoToast("Form reset", "All fields have been cleared."),
  },
};
