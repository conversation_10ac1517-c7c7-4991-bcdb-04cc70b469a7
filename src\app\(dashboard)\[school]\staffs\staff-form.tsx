"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { toast } from "sonner";
import {
  FormInput,
  FormSelect,
  FormRadioGroup,
  FormCombobox,
  FormDatePicker,
} from "@/components/forms";
import z from "zod";
import { getNationality } from "@/data/getNationality";
import { genderOptions, staffStatus, staffType } from "@/data/options";
import EnrolmentFormSubitButton from "../enrollment/_components/form/submit-button";
import { designationOptions, staffDepartmentOptions } from "@/lib/mock-data";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface StaffFormProps {
  initialData?: Partial<StaffFormData>;
}

const staffSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  middleName: z.string().min(2, "Middle name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  dateOfBirth: z.date({
    error: "Date of birth is required",
  }),
  gender: z.enum(["male", "female", "other"], {
    error: "Please select a gender",
  }),
  email: z.email("Invalid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  joinDate: z.date({
    error: "Join date is required",
  }),
  code: z.string().min(2, "Code must be at least 2 characters"),
  type: z.string().min(1, "Please select a type"),
  staffStatus: z.string().min(1, "Please select staff status"),
  department: z.string().min(1, "Please select a department"),
  designation: z.string().min(1, "Please select a designation"),
});

type StaffFormData = z.infer<typeof staffSchema>;

export function StaffForm({ initialData }: StaffFormProps) {
  const [nationalityData, setNationalityData] = useState<
    { label: string; value: string }[]
  >([]);

  const form = useForm<StaffFormData>({
    resolver: zodResolver(staffSchema),
    defaultValues: {
      firstName: "",
      middleName: "",
      lastName: "",
      gender: undefined,
      dateOfBirth: new Date(),
      phone: "",
      email: "",
      code: "",
      joinDate: new Date(),
      type: undefined,
      staffStatus: undefined,
      department: undefined,
      designation: undefined,
      ...initialData,
    },
  });

  // Reset form when initialData changes (for edit mode)
  useEffect(() => {
    if (initialData) {
      form.reset(initialData);
    }

    const fetchNationality = async () => {
      try {
        const data = await getNationality();
        setNationalityData(data);
      } catch (error) {
        console.error("Failed to fetch nationality data:", error);
      }
    };
    fetchNationality();
  }, [initialData, form]);

  const handleSubmit = async (data: StaffFormData) => {
    try {
      console.log(data);
      // await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error("Form submission failed", {
        description: "Please check your input and try again.",
      });
    }
  };

  const handleReset = () => {
    form.reset();
    toast.info("Form reset", {
      description: "All fields have been cleared.",
    });
  };

  return (
    <Card className="w-full">
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Staff rmation */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Staff rmation</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  form={form}
                  name="firstName"
                  label="First Name"
                  placeholder="Enter first name"
                />
                <FormInput
                  form={form}
                  name="middleName"
                  label="Middle Name"
                  placeholder="Enter middle name"
                />
                <FormInput
                  form={form}
                  name="lastName"
                  label="Last Name"
                  placeholder="Enter last name"
                />
                <FormRadioGroup
                  form={form}
                  name="gender"
                  label="Gender"
                  options={genderOptions}
                  orientation="horizontal"
                />
                <FormInput
                  form={form}
                  name="code"
                  label="Staff Code"
                  placeholder="Enter middle name"
                />
                <FormDatePicker
                  form={form}
                  name="dateOfBirth"
                  label="Date of Birth"
                  placeholder="Select date of birth"
                />
                <FormInput
                  form={form}
                  name="email"
                  label="Email Address"
                  type="email"
                  placeholder="<EMAIL>"
                />
                <FormInput
                  form={form}
                  name="phone"
                  label="Phone Number"
                  type="tel"
                  placeholder="(*************"
                />
                <FormDatePicker
                  form={form}
                  name="joinDate"
                  label="Date of Joining"
                  placeholder="Select date of birth"
                />
                <FormSelect
                  form={form}
                  name="type"
                  label="Type"
                  placeholder="Select type"
                  options={staffType}
                />
                <FormSelect
                  form={form}
                  name="staffStatus"
                  label="Staff Status"
                  placeholder="Select staff status"
                  options={staffStatus}
                />
                <FormSelect
                  form={form}
                  name="department"
                  label="Department"
                  placeholder="Select department"
                  options={staffDepartmentOptions}
                />
                <FormSelect
                  form={form}
                  name="designation"
                  label="Designation"
                  placeholder="Select designation"
                  options={designationOptions}
                />
              </div>
            </div>

            {/* Form Actions */}

            <div className="flex gap-4 pt-4 items-center">
              <Button
                type="submit"
                disabled={form.formState.isSubmitting}
                className="flex-1 md:flex-none"
              >
                {form.formState.isSubmitting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <>Submit</>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={handleReset}
                disabled={form.formState.isSubmitting}
              >
                Reset Form
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
