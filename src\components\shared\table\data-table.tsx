"use client";

import { Download, Edit, Eye, Trash2 } from "lucide-react";
import { TableBody } from "./body";
import { BulkActionsBar } from "./bulk-action";
import { FilterPanel } from "./filter";
import { TableHeader } from "./header";
import { TableHeaderBar } from "./header-bar";
import { Pagination } from "./pagination";
import { SearchInput } from "./search";
import { useEffect, useState } from "react";
import { useDebounce } from "@/hooks/useDebounce";

type DataTableProps = {
  // Data & API
  fetchData: (params: any) => Promise<any>;
  // Configuration
  title?: string;
  description?: string;
  columns: any[];
  actions?: any[];
  bulkActions?: any[];
  filterConfig?: any[];
  // Customization
  renderCell?: (row: any, column: any) => React.ReactNode;
  searchPlaceholder?: string;
  emptyMessage?: string;
  pageSizeOptions?: number[];
  initialPageSize?: number;
  // Features
  enableSearch?: boolean;
  enableFilters?: boolean;
  enableBulkActions?: boolean;
  enableExport?: boolean;
  // Callbacks
  onRowAction?: (action: string, row: any) => void;
  onBulkAction?: (action: string, rows: any[]) => void;
  headerActions?: any[];
};

export function DataTable({
  // Data & API
  fetchData,

  // Configuration
  title = "Data Table",
  description,
  columns = [],
  actions = [],
  bulkActions = [],
  filterConfig = [],

  // Customization
  renderCell,
  searchPlaceholder = "Search...",
  emptyMessage = "No data found",
  pageSizeOptions = [5, 10, 25, 50],
  initialPageSize = 10,

  // Features
  enableSearch = true,
  enableFilters = true,
  enableBulkActions = true,
  enableExport = true,

  // Callbacks
  onRowAction,
  onBulkAction,
  headerActions = [],
}: DataTableProps) {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: initialPageSize,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  });

  const [search, setSearch] = useState("");
  const [filters, setFilters] = useState({});
  const [sortBy, setSortBy] = useState("");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [selectedRows, setSelectedRows] = useState(new Set());
  const [showFilters, setShowFilters] = useState(false);

  const debouncedSearch = useDebounce(search, 300);

  const loadData = async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        search: debouncedSearch,
        filters,
        sortBy,
        sortOrder,
      };

      const result = await fetchData(params);
      setData(result.data);
      setPagination((prev) => ({
        ...prev,
        total: result.total,
        totalPages: result.totalPages,
        hasNextPage: result.hasNextPage,
        hasPreviousPage: result.hasPreviousPage,
      }));
    } catch (error) {
      console.error("Error loading data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [
    pagination.page,
    pagination.pageSize,
    debouncedSearch,
    filters,
    sortBy,
    sortOrder,
  ]);

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("asc");
    }
  };

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
    setSelectedRows(new Set());
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPagination((prev) => ({ ...prev, pageSize: newPageSize, page: 1 }));
    setSelectedRows(new Set());
  };

  const handleRowSelect = (rowId: string | number) => {
    const newSelected = new Set(selectedRows);
    if (newSelected.has(rowId)) {
      newSelected.delete(rowId);
    } else {
      newSelected.add(rowId);
    }
    setSelectedRows(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedRows.size === data.length) {
      setSelectedRows(new Set());
    } else {
      setSelectedRows(new Set(data.map((row) => (row as any).id)));
    }
  };

  const defaultHeaderActions = [
    ...(enableExport
      ? [
          {
            key: "export",
            label: "Export",
            icon: <Download className="w-4 h-4" />,
            onClick: () => console.log("Export clicked"),
          },
        ]
      : []),
    ...headerActions,
  ];

  const defaultActions = [
    {
      key: "view",
      label: "View",
      icon: <Eye className="w-4 h-4" />,
      variant: "primary",
    },
    {
      key: "edit",
      label: "Edit",
      icon: <Edit className="w-4 h-4" />,
    },
    {
      key: "delete",
      label: "Delete",
      icon: <Trash2 className="w-4 h-4" />,
      variant: "danger",
    },
    ...actions,
  ];

  const defaultBulkActions = [
    {
      key: "bulk-edit",
      label: "Bulk Edit",
      onClick: () =>
        onBulkAction && onBulkAction("bulk-edit", Array.from(selectedRows)),
    },
    {
      key: "bulk-delete",
      label: "Delete Selected",
      variant: "danger",
      onClick: () =>
        onBulkAction && onBulkAction("bulk-delete", Array.from(selectedRows)),
    },
    ...bulkActions,
  ];

  return (
    <div className="w-full mx-auto p-6 bg-white rounded-lg shadow-sm">
      <TableHeaderBar
        title={title}
        description={description}
        onRefresh={loadData}
        loading={loading}
        actions={defaultHeaderActions}
      />

      {/* Search and Filters */}
      <div className="mb-6 space-y-4">
        <div className="flex items-center gap-4">
          {enableSearch && (
            <SearchInput
              value={search}
              onChange={setSearch}
              placeholder={searchPlaceholder}
            />
          )}
          {enableFilters && filterConfig.length > 0 && (
            <FilterPanel
              filters={filters}
              onFiltersChange={setFilters}
              filterConfig={filterConfig}
              showFilters={showFilters}
              onToggleFilters={() => setShowFilters(!showFilters)}
            />
          )}
        </div>
      </div>

      {/* Bulk Actions */}
      {enableBulkActions && (
        <BulkActionsBar
          selectedCount={selectedRows.size}
          bulkActions={defaultBulkActions}
        />
      )}

      {/* Table */}
      <div className="overflow-hidden border border-gray-200 rounded-lg">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <TableHeader
              columns={columns}
              enableBulkActions={enableBulkActions}
              selectedCount={selectedRows.size}
              totalCount={data.length}
              onSelectAll={handleSelectAll}
              sortBy={sortBy}
              sortOrder={sortOrder}
              onSort={handleSort}
            />
            <TableBody
              data={data}
              columns={columns}
              loading={loading}
              enableBulkActions={enableBulkActions}
              selectedRows={selectedRows}
              onRowSelect={handleRowSelect}
              onRowAction={onRowAction}
              actions={defaultActions}
              renderCell={renderCell}
              emptyMessage={emptyMessage}
            />
          </table>
        </div>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={pagination.page}
        pageSize={pagination.pageSize}
        total={pagination.total}
        totalPages={pagination.totalPages}
        hasNextPage={pagination.hasNextPage}
        hasPreviousPage={pagination.hasPreviousPage}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        pageSizeOptions={pageSizeOptions}
      />
    </div>
  );
}
