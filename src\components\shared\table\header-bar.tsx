"use client";

import { But<PERSON> } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";

export function TableHeaderBar({
  title,
  description,
  onRefresh,
  loading,
  actions = [],
}: {
  title: string;
  description?: string;
  onRefresh: () => void;
  loading: boolean;
  actions?: {
    key: string;
    label: string;
    onClick: () => void;
    variant?: "primary" | "secondary";
    icon?: React.ReactNode;
  }[];
}) {
  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-2">
        <div>
          <h2 className="text-2xl font-semibold">{title}</h2>
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
        <div className="flex items-center gap-2">
          {actions.map((action) => (
            <Button
              key={action.key}
              onClick={action.onClick}
              variant={action.variant === "primary" ? "default" : "secondary"}
            >
              {action.icon && <span className="mr-2">{action.icon}</span>}
              {action.label}
            </Button>
          ))}
          <Button
            onClick={onRefresh}
            disabled={loading}
            className="disabled:opacity-50"
          >
            <RefreshCw
              className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
        </div>
      </div>
    </div>
  );
}
