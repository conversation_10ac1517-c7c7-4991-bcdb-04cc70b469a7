version: '3.8'

services:
  postgres:
    image: postgres:17-alpine
    container_name: kingdom_sis_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: kingdom_sis
      POSTGRES_USER: kingdom_user
      POSTGRES_PASSWORD: kingdom_password_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - kingdom_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U kingdom_user -d kingdom_sis"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Optional: pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: kingdom_sis_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - kingdom_network
    depends_on:
      postgres:
        condition: service_healthy

  # Optional: Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: kingdom_sis_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - kingdom_network
    command: redis-server --appendonly yes --requirepass redis_password_2024

volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local
  redis_data:
    driver: local

networks:
  kingdom_network:
    driver: bridge
