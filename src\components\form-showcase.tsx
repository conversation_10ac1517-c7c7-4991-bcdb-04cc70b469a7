"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  FormInput,
  FormTextarea,
  FormSelect,
  FormCheckbox,
  FormRadioGroup,
  FormCombobox,
  FormDatePicker,
  FormMultiSelect,
  FormRichTextEditor,
} from "@/components/forms";

// Demo schema showcasing all form components
const showcaseSchema = z.object({
  textInput: z.string().min(1, "Text input is required"),
  emailInput: z.email("Invalid email"),
  numberInput: z.number().min(1, "Must be at least 1"),
  textarea: z.string().min(10, "Must be at least 10 characters"),
  select: z.string().min(1, "Please select an option"),
  combobox: z.string().min(1, "Please select from combobox"),
  multiSelect: z.array(z.string()).min(1, "Select at least one option"),
  checkbox: z.boolean(),
  radioGroup: z.enum(["option1", "option2", "option3"]),
  datePicker: z.date({ error: "Date is required" }),
  richText: z.string().optional(),
});

type ShowcaseFormData = z.infer<typeof showcaseSchema>;

const selectOptions = [
  { value: "option1", label: "Option 1" },
  { value: "option2", label: "Option 2" },
  { value: "option3", label: "Option 3" },
];

const comboboxOptions = [
  { value: "apple", label: "Apple" },
  { value: "banana", label: "Banana" },
  { value: "cherry", label: "Cherry" },
  { value: "date", label: "Date" },
  { value: "elderberry", label: "Elderberry" },
];

const multiSelectOptions = [
  { value: "red", label: "Red" },
  { value: "green", label: "Green" },
  { value: "blue", label: "Blue" },
  { value: "yellow", label: "Yellow" },
  { value: "purple", label: "Purple" },
];

const radioOptions = [
  {
    value: "option1",
    label: "First Option",
    description: "This is the first option",
  },
  {
    value: "option2",
    label: "Second Option",
    description: "This is the second option",
  },
  {
    value: "option3",
    label: "Third Option",
    description: "This is the third option",
  },
];

export function FormShowcase() {
  const form = useForm<ShowcaseFormData>({
    resolver: zodResolver(showcaseSchema),
    // defaultValues: {
    //   textInput: "",
    //   emailInput: "",
    //   numberInput: 0,
    //   textarea: "",
    //   select: "",
    //   combobox: "",
    //   multiSelect: [],
    //   checkbox: false,
    //   radioGroup: undefined,
    //   richText: "",
    // },
  });

  const onSubmit = (data: ShowcaseFormData) => {
    console.log("Form data:", data);
    toast.success("Form submitted successfully!", {
      description: "Check the console to see the submitted data.",
    });
  };

  const handleReset = () => {
    form.reset();
    toast.info("Form reset", {
      description: "All fields have been cleared.",
    });
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Form Components Showcase</CardTitle>
        <CardDescription>
          Demonstration of all available reusable form components with
          validation.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Input Components */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Input Components</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormInput
                  form={form}
                  name="textInput"
                  label="Text Input"
                  placeholder="Enter text"
                  description="Basic text input field"
                />
                <FormInput
                  form={form}
                  name="emailInput"
                  label="Email Input"
                  type="email"
                  placeholder="Enter email"
                  description="Email validation included"
                />
                <FormInput
                  form={form}
                  name="numberInput"
                  label="Number Input"
                  type="number"
                  placeholder="Enter number"
                  description="Number input with validation"
                />
              </div>
            </div>

            <Separator />

            {/* Text Area */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Text Area</h3>
              <FormTextarea
                form={form}
                name="textarea"
                label="Textarea"
                placeholder="Enter longer text here..."
                rows={4}
                description="Multi-line text input"
              />
            </div>

            <Separator />

            {/* Selection Components */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Selection Components</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormSelect
                  form={form}
                  name="select"
                  label="Select Dropdown"
                  placeholder="Choose an option"
                  options={selectOptions}
                  description="Standard dropdown select"
                />
                <FormCombobox
                  form={form}
                  name="combobox"
                  label="Combobox"
                  placeholder="Search and select..."
                  options={comboboxOptions}
                  searchPlaceholder="Search fruits..."
                  description="Searchable dropdown"
                />
              </div>
              <FormMultiSelect
                form={form}
                name="multiSelect"
                label="Multi-Select"
                placeholder="Select multiple colors"
                options={multiSelectOptions}
                searchPlaceholder="Search colors..."
                maxSelected={3}
                description="Select up to 3 colors"
              />
            </div>

            <Separator />

            {/* Choice Components */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Choice Components</h3>
              <FormCheckbox
                form={form}
                name="checkbox"
                label="I agree to the terms and conditions"
                description="Single checkbox for agreements"
              />
              <FormRadioGroup
                form={form}
                name="radioGroup"
                label="Radio Group"
                options={radioOptions}
                description="Single choice from multiple options"
              />
            </div>

            <Separator />

            {/* Date Picker */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Date Selection</h3>
              <FormDatePicker
                form={form}
                name="datePicker"
                label="Date Picker"
                placeholder="Select a date"
                description="Calendar date picker"
              />
            </div>

            <Separator />

            {/* Rich Text Editor */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Rich Text Editor</h3>
              <FormRichTextEditor
                form={form}
                name="richText"
                label="Rich Text Editor"
                placeholder="Start typing with rich formatting..."
                description="Basic rich text editor with formatting options"
                minHeight="150px"
              />
            </div>

            {/* Form Actions */}
            <div className="flex gap-4 pt-4">
              <Button type="submit" className="flex-1 md:flex-none">
                Submit Form
              </Button>
              <Button type="button" variant="outline" onClick={handleReset}>
                Reset Form
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
