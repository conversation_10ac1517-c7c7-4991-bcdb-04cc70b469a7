"use client";

import type React from "react";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Menu } from "lucide-react";
import { cn } from "@/lib/utils";
import { ProtectedRoute } from "@/app/(auth)/_components/protected-route";
import { DynamicSidebar } from "../navigation/dynamic-sidebar";
import { AppHeader } from "../navigation/app-header";

interface DashboardLayoutProps {
  children: React.ReactNode;
  requiredRole?: string[];
  requiredPermission?: { resource: string; action: string };
}

export function DashboardLayout({
  children,
  requiredRole,
  requiredPermission,
}: DashboardLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <ProtectedRoute
      requiredRole={requiredRole}
      requiredPermission={requiredPermission}
    >
      <div className="flex h-screen bg-background">
        {/* Desktop Sidebar */}
        <div className="hidden md:block">
          <DynamicSidebar
            collapsed={sidebarCollapsed}
            onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
          />
        </div>

        {/* Mobile Sidebar Overlay */}
        {mobileMenuOpen && (
          <div className="fixed inset-0 z-50 md:hidden">
            <div
              className="absolute inset-0 bg-black/50"
              onClick={() => setMobileMenuOpen(false)}
            />
            <div className="absolute left-0 top-0 h-full">
              <DynamicSidebar />
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Mobile Header with Menu Button */}
          <div className="md:hidden border-b bg-background px-4 py-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setMobileMenuOpen(true)}
              className="h-8 w-8 p-0"
            >
              <Menu className="h-4 w-4" />
            </Button>
          </div>

          <AppHeader />
          <main
            className={cn(
              "flex-1 overflow-y-auto p-6",
              sidebarCollapsed && "md:ml-0"
            )}
          >
            {children}
          </main>
        </div>
      </div>
    </ProtectedRoute>
  );
}
