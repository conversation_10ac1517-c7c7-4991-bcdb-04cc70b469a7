"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import EnrolmentFormSubitButton from "./submit-button";
import { Donut, Info } from "lucide-react";
import z from "zod";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";

const documentUploadSchema = z.object({
  birthCertificate: z.any(),
  baptismalCertificate: z.any().optional(),
  kinderCOC: z.any(),
  latestReportCard: z.any(),
  idPicture: z.any(),
  acceptTerms: z.boolean(),
});

type DocumentUploadFormData = z.infer<typeof documentUploadSchema>;

interface DocumentUploadFormProps {
  initialData?: Partial<DocumentUploadFormData>;
}

export default function DocumentUploadForm({
  initialData,
}: DocumentUploadFormProps) {
  const form = useForm<DocumentUploadFormData>({
    resolver: zodResolver(documentUploadSchema),
    defaultValues: {},
  });

  useEffect(() => {
    if (initialData) {
      form.reset(initialData);
    }
  }, [initialData, form]);

  const handleSubmit = async (data: DocumentUploadFormData) => {
    try {
      console.log(data);
      // await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error("Form submission failed", {
        description: "Please check your input and try again.",
      });
    }
  };

  return (
    <Card className="w-full">
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Document Upload */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Document Upload</h3>
              <div className="flex flex-col p-5 rounded-lg bg-accent text-accent-foreground flex-1 gap-5">
                <h4 className="flex gap-2 font-semibold text-lg">
                  <Info size="1.6em" /> Notes
                </h4>

                <div className="flex flex-col gap-3">
                  <p className="flex gap-1 sm:items-center">
                    <Donut />
                    Make sure to upload the documents according to the spaces
                    given below.
                  </p>
                  <p className="flex gap-1 sm:items-center">
                    <Donut />
                    You might be asked to present the original copy of the
                    documents to the School Registrar on your campus visit.
                  </p>
                  <p className="flex gap-1 sm:items-center">
                    <Donut />
                    Only files with the following file extensions are allowed :
                    PDF, Excel, Docs, JPG, JPEG and PNG. Total file size must
                    NOT be greater than 5 MB.
                  </p>
                </div>
              </div>
            </div>

            <h3 className="text-primary mt-8">REQUIREMENT NAME</h3>

            <div className="flex flex-col gap-4">
              <FormField
                control={form.control}
                name="birthCertificate"
                render={({ field: { value, onChange, ...field } }) => (
                  <FormItem>
                    <div className="sm:flex items-center">
                      <FormLabel className="flex-1">
                        1. PSA Birth Certificate
                      </FormLabel>
                      <FormControl className="flex-1 cursor-pointer">
                        <Input
                          {...field}
                          value={value?.fileName}
                          onChange={(e) => {
                            onChange(e.target.files && e.target.files[0]);
                          }}
                          className="input-field"
                          type="file"
                        />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="baptismalCertificate"
                render={({ field: { value, onChange, ...field } }) => (
                  <FormItem>
                    <div className="sm:flex items-center">
                      <FormLabel className="flex-1">
                        2. Baptismal Certificate
                      </FormLabel>
                      <FormControl className="flex-1 cursor-pointer">
                        <Input
                          {...field}
                          onChange={(e) => {
                            onChange(e.target.files && e.target.files[0]);
                          }}
                          className="input-field"
                          type="file"
                        />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="kinderCOC"
                render={({ field: { value, onChange, ...field } }) => (
                  <FormItem>
                    <div className="sm:flex items-center">
                      <FormLabel className="flex-1">
                        3. Kinder Certificate of Completion
                      </FormLabel>
                      <FormControl className="flex-1 cursor-pointer">
                        <Input
                          {...field}
                          value={value?.fileName}
                          onChange={(e) => {
                            onChange(e.target.files && e.target.files[0]);
                          }}
                          className="input-field"
                          type="file"
                        />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="latestReportCard"
                render={({ field: { value, onChange, ...field } }) => (
                  <FormItem>
                    <div className="sm:flex items-center">
                      <FormLabel className="flex-1">
                        4. Latest Report Card / SF 9 (Learner&apos;s Progress
                        Report w/ LRN)
                      </FormLabel>
                      <FormControl className="flex-1 cursor-pointer">
                        <Input
                          {...field}
                          value={value?.fileName}
                          onChange={(e) => {
                            onChange(e.target.files && e.target.files[0]);
                          }}
                          className="input-field"
                          type="file"
                        />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="idPicture"
                render={({ field: { value, onChange, ...field } }) => (
                  <FormItem>
                    <div className="sm:flex items-center">
                      <FormLabel className="flex-1">
                        5. 2x2 I.D Picture (white background) with colla
                      </FormLabel>
                      <FormControl className="flex-1 cursor-pointer">
                        <Input
                          {...field}
                          value={value?.fileName}
                          onChange={(e) => {
                            onChange(e.target.files && e.target.files[0]);
                          }}
                          className="input-field"
                          type="file"
                        />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="acceptTerms"
              render={({ field }) => (
                <FormItem>
                  <div className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 shadow mt-6">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-2 leading-none">
                      <FormLabel>
                        I / we certify that the data given herein and the
                        accompanying documents are true and correct to the best
                        of my / our knowledge and thus, misrepresentation is a
                        sufficient reason for non-admission.
                      </FormLabel>
                      <FormDescription className="text-xs">
                        All information such as, but not limited to the creation
                        and maintenance of student&apos;s and personnel&apos;s
                        records for educational and other legitimate purposes in
                        written, electronic or recorded means held by Holy
                        Family School of Quezon City, Inc. shall be processed by
                        authorized personnel in accordance with the data privacy
                        policies of the institutions (RA #10173 or known as the
                        “Data Privacy Act of 2012”). I do hereby allow/authorize
                        Holy Family School of Quezon City, Inc. to use, collect
                        and process the above information for educational and
                        other legitimate purposes. **Please do note that your
                        application to (school name) will be on provisional
                        status until you have submitted your requirements in
                        person.
                      </FormDescription>
                    </div>
                  </div>
                </FormItem>
              )}
            />

            {/* Form Actions */}
            <EnrolmentFormSubitButton
              isSumitting={form.formState.isSubmitting}
            />
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
