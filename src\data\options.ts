export const gradeOptions = [
  { value: "kindergarten", label: "Kindergarten" },
  { value: "1", label: "Grade 1" },
  { value: "2", label: "Grade 2" },
  { value: "3", label: "Grade 3" },
  { value: "4", label: "Grade 4" },
  { value: "5", label: "Grade 5" },
  { value: "6", label: "Grade 6" },
  { value: "7", label: "Grade 7" },
  { value: "8", label: "Grade 8" },
  { value: "9", label: "Grade 9" },
  { value: "10", label: "Grade 10" },
  { value: "11", label: "Grade 11" },
  { value: "12", label: "Grade 12" },
];

export const studentType = [
  { value: "new", label: "New" },
  { value: "returning", label: "Returning" },
];

export const religiousAffiliation = [
  { label: "Christianity", value: "christianity" },
  { label: "Islam", value: "islam" },
  { label: "Judaism", value: "judaism" },
];

export const genderOptions = [
  { value: "male", label: "Male" },
  { value: "female", label: "Female" },
  { value: "other", label: "Other" },
];

export const enrollReason = [
  { label: "Reason 1", value: "reason1" },
  { label: "Reason 2", value: "reason2" },
  { label: "Reason 3", value: "reason3" },
  { label: "Other", value: "other" },
];

export const educationLevel = [
  { label: "Primary", value: "primary" },
  { label: "Junior Secondary", value: "juniorSecondary" },
  { label: "Senior Secondary", value: "seniorSecondary" },
];

export const emergencyContact = [
  { label: "Father", value: "father" },
  { label: "Mother", value: "mother" },
  { label: "Gurdian", value: "gurdian" },
];

export const maritalStatus = [
  { label: "Single", value: "single" },
  { label: "Married", value: "married" },
  { label: "Separated", value: "separated" },
]

export const childCustodian = [
  { label: 'Option 1', value: 'option1' },
  { label: 'Option 2', value: 'option2' },
  { label: 'Option 3', value: 'option3' },
]

export const Sources = [
  {
    id: "sourcesEmployee",
    label: "Employee of the school",
  },
  {
    id: "sourcesAlumni",
    label: "Alumni",
  },
  {
    id: "sourcesTarpaulin",
    label: "School Tarpaulin",
  },
  {
    id: "sourcesFamily",
    label: "Family",
  },
  {
    id: "sourcesFriends",
    label: "Friends",
  },
  {
    id: "sourcesFacebook",
    label: "Facebook",
  },
  {
    id: "sourcesInternet",
    label: "Website/Internet",
  },
  {
    id: "sourcesPasserBy",
    label: "Passer by",
  },
  {
    id: "sourcesOther",
    label: "Others (Please specify)",
  },
] as const;

export const Motivation = [
  {
    id: "motivationOPT1",
    label: "Influence by parent/relatives/friends",
  },
  {
    id: "motivationOPT2",
    label: "Accessibility and Location",
  },
  {
    id: "motivationOPT3",
    label: "Religion as the Core Curriculum",
  },
  {
    id: "motivationOPT4",
    label: "School Discipline",
  },
  {
    id: "motivationOPT5",
    label: "Well-Known School",
  },
  {
    id: "motivationOPT6",
    label: "Competence of Teacher",
  },
  {
    id: "motivationOPT7",
    label: "School Facility",
  },
  {
    id: "motivationOPT8",
    label: "Reasonable Tuition Fee",
  },
  {
    id: "motivationOPT9",
    label: "Campus Security",
  },
  {
    id: "motivationOPT10",
    label: "Quality/Standards of Education",
  },
  {
    id: "motivationOther",
    label: "Others (Please specify)",
  },
] as const;