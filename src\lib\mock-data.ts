import type {
  User,
  Admin,
  Teacher,
  Student,
  Parent,
  Class,
  Subject,
  Assignment,
  Grade,
  Attendance,
  SuperAdmin,
  Academic_Department,
  Staff_Department,
  Designation,
  Staff,
} from "./types";
import { SYSTEM_ROLES } from "./permissions";
import { staffStatus, staffType } from "@/data/options";

// Mock Users
export const mockUsers: User[] = [
  {
    id: "1",
    email: "<EMAIL>",
    name: "<PERSON>",
    role: "super_admin",
    permissions: SYSTEM_ROLES.SUPER_ADMIN.permissions,
    avatar: "/professional-woman-admin.png",
    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  } as SuperAdmin,
  {
    id: "2",
    email: "<EMAIL>",
    name: "<PERSON>",
    role: "admin",
    permissions: SYSTEM_ROLES.ADMIN.permissions,
    avatar: "/professional-woman-admin.png",

    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  } as Admin,
  {
    id: "3",
    email: "<EMAIL>",
    name: "<PERSON>",
    role: "teacher",
    permissions: SYSTEM_ROLES.TEACHER.permissions,
    avatar: "/male-teacher.png",

    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  } as Teacher,
  {
    id: "4",
    email: "<EMAIL>",
    name: "Emily Davis",
    role: "student",
    permissions: SYSTEM_ROLES.STUDENT.permissions,
    avatar: "/diverse-female-student.png",

    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  } as Student,
  {
    id: "5",
    email: "<EMAIL>",
    name: "Robert Davis",
    role: "parent",
    permissions: SYSTEM_ROLES.PARENT.permissions,
    avatar: "/parent-father.png",

    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  } as Parent,
  {
    id: "6",
    email: "<EMAIL>",
    name: "Alice Doe",
    role: "applicant",
    permissions: SYSTEM_ROLES.APPLICANT.permissions,
    avatar: "/images/placeholder.svg",

    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
];

// Mock detailed user data
export const mockSuperAdmins: SuperAdmin[] = [
  {
    ...mockUsers[0],
    role: "super_admin",
  } as SuperAdmin,
];

export const mockAdmins: Admin[] = [
  {
    ...mockUsers[1],
    role: "admin",
  } as Admin,
];

export const mockTeachers: Teacher[] = [
  {
    ...mockUsers[2],
    role: "teacher",
    employeeId: "T001",
    subjects: ["mathematics", "physics", "english"],
    classes: ["10A", "11B"],
    department: "Science",
  } as Teacher,
];

export const mockStudents: Student[] = [
  {
    ...mockUsers[3],
    role: "student",
    studentId: "S001",
    grade: "10",
    class: "10A",
    parentId: "4",
    dateOfBirth: new Date("2008-05-15"),
  } as Student,
];

export const mockParents: Parent[] = [
  {
    ...mockUsers[4],
    role: "parent",
    children: ["3"],
    phone: "******-0123",
  } as Parent,
];

// Mock Classes
export const mockClasses: Class[] = [
  {
    id: "1",
    name: "10A",
    grade: "10",
    teacherId: "3",
    students: ["4"],
    subjects: ["mathematics", "physics", "english"],
  },
];

// Mock Subjects
export const mockSubjects: Subject[] = [
  {
    id: "1",
    name: "Mathematics",
    code: "MATH10",
    teacherId: "3",
    classes: ["10A"],
  },
  {
    id: "2",
    name: "Physics",
    code: "PHYS10",
    teacherId: "3",
    classes: ["10A"],
  },
];

// Mock Assignments
export const mockAssignments: Assignment[] = [
  {
    id: "1",
    title: "Algebra Quiz",
    description: "Basic algebra problems covering linear equations",
    subjectId: "1",
    classId: "1",
    teacherId: "2",
    dueDate: new Date("2024-02-15"),
    createdAt: new Date("2024-02-01"),
  },
];

// Mock Grades
export const mockGrades: Grade[] = [
  {
    id: "1",
    studentId: "3",
    assignmentId: "1",
    score: 85,
    maxScore: 100,
    feedback: "Good work! Focus on quadratic equations.",
    gradedAt: new Date("2024-02-16"),
  },
];

// Mock Attendance
export const mockAttendance: Attendance[] = [
  {
    id: "1",
    studentId: "3",
    classId: "1",
    date: new Date("2024-02-01"),
    status: "present",
  },
];

// Mock Academic Departments
export const mockDepartments: Academic_Department[] = [
  {
    id: "1",
    name: "Computer Science",
    code: "CS",
    alias: "CompSci",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "2",
    name: "Mathematics",
    code: "MATH",
    alias: "Math",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "3",
    name: "Physics",
    code: "PHYS",
    alias: "Physics",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "4",
    name: "Chemistry",
    code: "CHEM",
    alias: "Chem",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "5",
    name: "Biology",
    code: "BIO",
    alias: "Bio",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "6",
    name: "Engineering",
    code: "ENG",
    alias: "Engineering",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "7",
    name: "Business Administration",
    code: "BUS",
    alias: "Business",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "8",
    name: "Fine Arts",
    code: "ARTS",
    alias: "Arts",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "9",
    name: "English Literature",
    code: "ENG-LIT",
    alias: "English",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "10",
    name: "History",
    code: "HIST",
    alias: "History",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "11",
    name: "Psychology",
    code: "PSYC",
    alias: "Psychology",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "12",
    name: "Physical Education",
    code: "PE",
    alias: "PhysEd",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
];

export const mockStaffDepartments: Staff_Department[] = [
  {
    id: "1",
    name: "Administrative",
    alias: "Admin",
    description: "Administrative Department",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "2",
    name: "Registrar",
    alias: "Reg",
    description: "Registrar Department",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "3",
    name: "Teaching",
    alias: "Teach",
    description: "Teaching Department",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "4",
    name: "Human Resource",
    alias: "HR",
    description: "Human Resource Department",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "5",
    name: "Finance",
    alias: "Fin",
    description: "Finance Department",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "6",
    name: "IT",
    alias: "IT",
    description: "IT Department",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "7",
    name: "Marketing",
    alias: "Mark",
    description: "Marketing Department",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
];

export const mockDesignations: Designation[] = [
  {
    id: "1",
    name: "Administrator",
    alias: "Admin",
    parent: null,
    description: "Admin Designation",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "2",
    name: "Director",
    alias: "Dir",
    parent: {
      id: "1",
      name: "Administrator",
      alias: "Admin",
    },
    description: "Director Designation",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "3",
    name: "Manager",
    alias: "Mgr",
    parent: {
      id: "2",
      name: "Director",
      alias: "Dir",
    },
    description: "Manager Designation",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "4",
    name: "Principal",
    alias: "Principal",
    parent: {
      id: "3",
      name: "Manager",
      alias: "Mgr",
    },
    description: "Principal Designation",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "5",
    name: "Teacher",
    alias: "Teacher",
    parent: {
      id: "4",
      name: "Principal",
      alias: "Principal",
    },
    description: "Teacher Designation",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "6",
    name: "Helper",
    alias: "Helper",
    parent: {
      id: "2",
      name: "Director",
      alias: "Director",
    },
    description: "Helper Designation",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "7",
    name: "Driver",
    alias: "Driver",
    parent: {
      id: "2",
      name: "Director",
      alias: "Director",
    },
    description: "Driver Designation",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  }
];

export const mockStaffs: Staff[] = [
  {
    id: "1",
    firstName: "John",
    middleName: "D",
    lastName: "Doe",
    gender: "male",
    dateOfBirth: new Date("1980-01-01"),
    phone: "1234567890",
    email: "<EMAIL>",
    code: "S001",
    joinDate: new Date("2020-01-01"),
    type: staffType[0].value,
    staffStatus: staffStatus[0].value,
    department: mockStaffDepartments[0].id,
    designation: mockDesignations[0].id,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  }
]

// Helper functions
export const getUserById = (id: string): User | undefined => {
  return mockUsers.find((user) => user.id === id);
};

export const getUserByEmail = (email: string): User | undefined => {
  return mockUsers.find((user) => user.email === email);
};

export const getClassesByTeacher = (teacherId: string): Class[] => {
  return mockClasses.filter((cls) => cls.teacherId === teacherId);
};

export const getStudentsByClass = (classId: string): Student[] => {
  const classData = mockClasses.find((cls) => cls.id === classId);
  if (!classData) return [];
  return mockStudents.filter((student) =>
    classData.students.includes(student.id),
  );
};

export const getDepartmentById = (id: string): Academic_Department | undefined => {
  return mockDepartments.find((department) => department.id === id);
};

export const getDepartmentByCode = (code: string): Academic_Department | undefined => {
  return mockDepartments.find((department) => department.code === code);
};

// Mock API function for academic departments (similar to real API)
export const getDepartments = async (): Promise<{
  data: Academic_Department[];
  total: number;
}> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  return {
    data: mockDepartments,
    total: mockDepartments.length,
  };
};

// Mock API function for staff departments (similar to real API)
export const getStaffDepartments = async (): Promise<{
  data: Staff_Department[];
  total: number;
}> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  return {
    data: mockStaffDepartments,
    total: mockStaffDepartments.length,
  };
};

export const staffDepartmentOptions = mockStaffDepartments.map((department) => ({
  label: department.name,
  value: department.id,
}));

// Mock API function for designations (similar to real API)
export const getDesignations = async (): Promise<{
  data: Designation[];
  total: number;
}> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  return {
    data: mockDesignations,
    total: mockDesignations.length,
  };
};

export const designationOptions = mockDesignations.map((designation) => ({
  label: designation.name,
  value: designation.id,
}));

export const getStaffs = async (): Promise<{
  data: Staff[];
  total: number;
}> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  return {
    data: mockStaffs,
    total: mockStaffs.length,
  };
};
