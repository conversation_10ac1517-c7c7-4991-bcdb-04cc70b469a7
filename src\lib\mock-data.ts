import type {
  User,
  Admin,
  Teacher,
  Student,
  Parent,
  Class,
  Subject,
  Assignment,
  Grade,
  Attendance,
  Permission,
  RolePermissions,
  Role,
  SuperAdmin,
} from "./types"
import { SYSTEM_ROLES } from "./permissions"

// Mock Users
export const mockUsers: User[] = [
  {
    id: "1",
    email: "<EMAIL>",
    name: "<PERSON>",
    role: "super_admin",
    permissions: SYSTEM_ROLES.SUPER_ADMIN.permissions,
    avatar: "/professional-woman-admin.png",
    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  } as SuperAdmin,
  {
    id: "2",
    email: "<EMAIL>",
    name: "<PERSON>",
    role: "admin",
    permissions: SYSTEM_ROLES.ADMIN.permissions,
    avatar: "/professional-woman-admin.png",
    
    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  } as Admin,
  {
    id: "3",
    email: "<EMAIL>",
    name: "<PERSON>",
    role: "teacher",
    permissions: SYSTEM_ROLES.TEACHER.permissions,
    avatar: "/male-teacher.png",
    
    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  } as Teacher,
  {
    id: "4",
    email: "<EMAIL>",
    name: "<PERSON>",
    role: "student",
    permissions: SYSTEM_ROLES.STUDENT.permissions,
    avatar: "/diverse-female-student.png",
    
    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  } as Student,
  {
    id: "5",
    email: "<EMAIL>",
    name: "Robert Davis",
    role: "parent",
    permissions: SYSTEM_ROLES.PARENT.permissions,
    avatar: "/parent-father.png",
    
    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  } as Parent,
  {
    id: "6",
    email: "<EMAIL>",
    name: "Alice Doe",
    role: "applicant",
    permissions: SYSTEM_ROLES.APPLICANT.permissions,
    avatar: "/images/placeholder.svg",
    
    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  }
]

// Mock detailed user data
export const mockSuperAdmins: SuperAdmin[] = [
  {
    ...mockUsers[0],
    role: "super_admin",
  } as SuperAdmin,
]

export const mockAdmins: Admin[] = [
  {
    ...mockUsers[1],
    role: "admin",
  } as Admin,
]

export const mockTeachers: Teacher[] = [
  {
    ...mockUsers[2],
    role: "teacher",
    employeeId: "T001",
    subjects: ["mathematics", "physics", "english"],
    classes: ["10A", "11B"],
    department: "Science",
  } as Teacher,
]

export const mockStudents: Student[] = [
  {
    ...mockUsers[3],
    role: "student",
    studentId: "S001",
    grade: "10",
    class: "10A",
    parentId: "4",
    dateOfBirth: new Date("2008-05-15"),
  } as Student,
]

export const mockParents: Parent[] = [
  {
    ...mockUsers[4],
    role: "parent",
    children: ["3"],
    phone: "******-0123",
  } as Parent,
]

// Mock Classes
export const mockClasses: Class[] = [
  {
    id: "1",
    name: "10A",
    grade: "10",
    teacherId: "3",
    students: ["4"],
    subjects: ["mathematics", "physics", "english"],
  },
]

// Mock Subjects
export const mockSubjects: Subject[] = [
  {
    id: "1",
    name: "Mathematics",
    code: "MATH10",
    teacherId: "3",
    classes: ["10A"],
  },
  {
    id: "2",
    name: "Physics",
    code: "PHYS10",
    teacherId: "3",
    classes: ["10A"],
  },
]

// Mock Assignments
export const mockAssignments: Assignment[] = [
  {
    id: "1",
    title: "Algebra Quiz",
    description: "Basic algebra problems covering linear equations",
    subjectId: "1",
    classId: "1",
    teacherId: "2",
    dueDate: new Date("2024-02-15"),
    createdAt: new Date("2024-02-01"),
  },
]

// Mock Grades
export const mockGrades: Grade[] = [
  {
    id: "1",
    studentId: "3",
    assignmentId: "1",
    score: 85,
    maxScore: 100,
    feedback: "Good work! Focus on quadratic equations.",
    gradedAt: new Date("2024-02-16"),
  },
]

// Mock Attendance
export const mockAttendance: Attendance[] = [
  {
    id: "1",
    studentId: "3",
    classId: "1",
    date: new Date("2024-02-01"),
    status: "present",
  },
]

// Helper functions
export const getUserById = (id: string): User | undefined => {
  return mockUsers.find((user) => user.id === id)
}

export const getUserByEmail = (email: string): User | undefined => {
  return mockUsers.find((user) => user.email === email)
}

export const getClassesByTeacher = (teacherId: string): Class[] => {
  return mockClasses.filter((cls) => cls.teacherId === teacherId)
}

export const getStudentsByClass = (classId: string): Student[] => {
  const classData = mockClasses.find((cls) => cls.id === classId)
  if (!classData) return []
  return mockStudents.filter((student) => classData.students.includes(student.id))
}
