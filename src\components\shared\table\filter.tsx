"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Filter } from "lucide-react";

type Props = {
  filters: Record<string, string>;
  onFiltersChange: (filters: Record<string, string | undefined>) => void;
  filterConfig: {
    key: string;
    label: string;
    placeholder: string;
    options: { value: string; label: string }[];
  }[];
  showFilters: boolean;
  onToggleFilters: () => void;
};

export function FilterPanel({
  filters,
  onFiltersChange,
  filterConfig,
  showFilters,
  onToggleFilters,
}: Props) {
  const activeFilterCount = Object.keys(filters).filter(
    (key) => filters[key],
  ).length;

  const handleFilterChange = (key: string, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value || undefined,
    });
  };

  const clearFilters = () => {
    onFiltersChange({});
  };

  return (
    <>
      <Button
        onClick={onToggleFilters}
        variant={showFilters || activeFilterCount > 0 ? "outline" : "ghost"}
      >
        <Filter className="w-4 h-4 mr-2" />
        Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
      </Button>

      {showFilters && (
        <div className="p-4 border rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {filterConfig.map((config) => (
              <div key={config.key}>
                <Label>{config.label}</Label>
                <Select
                  defaultValue={filters[config.key] || ""}
                  onValueChange={(e) => handleFilterChange(config.key, e)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a verified email to display" />
                  </SelectTrigger>
                  <SelectContent>
                    {config.options.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ))}
            <div className="flex items-end">
              <button
                onClick={clearFilters}
                className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
