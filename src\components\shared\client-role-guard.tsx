"use client";

import { ReactNode } from "react";
import { Actions } from "@/lib/permissions";
import { usePermissions } from "@/hooks/use-permissions";
import { Context } from "@/lib/types";

interface ClientRoleGuardProps {
  children: ReactNode;
  resource: string;
  action?: string;
  context?: Context;
  fallback?: ReactNode;
  showLoading?: boolean;
}

/**
 * Client-side component guard for dynamic UI elements
 * Use this for buttons, forms, and interactive elements that need real-time permission checking
 */
export function ClientRoleGuard({
  children,
  resource,
  action = Actions.READ,
  context,
  fallback = null,
  showLoading = false,
}: ClientRoleGuardProps) {
  const { hasPermission, user, isLoading } = usePermissions();

  if (isLoading && showLoading) {
    return <div className="animate-pulse bg-gray-200 h-8 w-24 rounded"></div>;
  }

  if (!user || isLoading) {
    return <>{fallback}</>;
  }

  const hasAccess = hasPermission(resource, action, context);

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}
