'use client';

import { useAuth } from '@/contexts/auth-context';
import { checkPermission, Actions, Resources } from '@/lib/permissions';

export function usePermissions() {
  const { user } = useAuth();

  const hasPermission = (
    resource: string, 
    action: string = Actions.READ, 
    context?: any
  ): boolean => {
    if (!user) return false;
    return checkPermission(user, resource, action, context);
  };

  const canCreate = (resource: string, context?: any) => 
    hasPermission(resource, Actions.CREATE, context);

  const canRead = (resource: string, context?: any) => 
    hasPermission(resource, Actions.READ, context);

  const canUpdate = (resource: string, context?: any) => 
    hasPermission(resource, Actions.UPDATE, context);

  const canDelete = (resource: string, context?: any) => 
    hasPermission(resource, Actions.DELETE, context);

  const canManage = (resource: string, context?: any) => 
    hasPermission(resource, Actions.MANAGE, context);

  // Specific permission checks
  const permissions = {
    // Dashboard
    canViewDashboard: () => hasPermission(Resources.DASHBOARD),
    
    // Students
    canViewStudents: () => hasPermission(Resources.STUDENTS),
    canCreateStudent: () => canCreate(Resources.STUDENTS),
    canEditStudent: (studentId?: string) => 
      canUpdate(Resources.STUDENTS, { studentId }),
    canDeleteStudent: (studentId?: string) => 
      canDelete(Resources.STUDENTS, { studentId }),
    
    // Teachers
    canViewTeachers: () => hasPermission(Resources.TEACHERS),
    canCreateTeacher: () => canCreate(Resources.TEACHERS),
    canEditTeacher: (teacherId?: string) => 
      canUpdate(Resources.TEACHERS, { teacherId }),
    
    // Classes
    canViewClasses: () => hasPermission(Resources.CLASSES),
    canCreateClass: () => canCreate(Resources.CLASSES),
    canManageClass: (classId?: string) => 
      canManage(Resources.CLASSES, { classId }),
    
    // Grades
    canViewGrades: (context?: any) => hasPermission(Resources.GRADES, Actions.READ, context),
    canEditGrades: (context?: any) => hasPermission(Resources.GRADES, Actions.UPDATE, context),
    
    // Settings
    canViewSettings: () => hasPermission(Resources.SETTINGS),
    canManageUsers: () => hasPermission(Resources.USERS, Actions.MANAGE),
    canManageRoles: () => hasPermission(Resources.ROLES, Actions.MANAGE),
    
    // Reports
    canViewReports: () => hasPermission(Resources.REPORTS),
  };

  return {
    hasPermission,
    canCreate,
    canRead,
    canUpdate,
    canDelete,
    canManage,
    ...permissions,
  };
}