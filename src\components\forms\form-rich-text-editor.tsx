"use client";

import { useRef } from "react";
import type { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { cn } from "@/lib/utils";

interface FormRichTextEditorProps {
  form: UseFormReturn<any>;
  name: string;
  label?: string;
  placeholder?: string;
  description?: string;
  disabled?: boolean;
  className?: string;
  minHeight?: string;
}

export function FormRichTextEditor({
  form,
  name,
  label,
  placeholder = "Start typing...",
  description,
  disabled,
  className,
  minHeight = "200px",
}: FormRichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <div className="relative">
              <div
                ref={editorRef}
                contentEditable={!disabled}
                className={cn(
                  "w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background",
                  "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                  "disabled:cursor-not-allowed disabled:opacity-50",
                  "prose prose-sm max-w-none",
                  field.value && "border-primary/50"
                )}
                style={{ minHeight }}
                onInput={(e) => {
                  const content = e.currentTarget.innerHTML;
                  field.onChange(content);
                }}
                onBlur={field.onBlur}
                dangerouslySetInnerHTML={{ __html: field.value || "" }}
                data-placeholder={placeholder}
              />
              {!field.value && (
                <div className="absolute top-2 left-3 text-sm text-muted-foreground pointer-events-none">
                  {placeholder}
                </div>
              )}
              <div className="flex items-center gap-1 p-2 border-t border-border bg-muted/50 rounded-b-md">
                <button
                  type="button"
                  className="px-2 py-1 text-xs rounded hover:bg-background"
                  onClick={() => document.execCommand("bold")}
                  disabled={disabled}
                >
                  <strong>B</strong>
                </button>
                <button
                  type="button"
                  className="px-2 py-1 text-xs rounded hover:bg-background"
                  onClick={() => document.execCommand("italic")}
                  disabled={disabled}
                >
                  <em>I</em>
                </button>
                <button
                  type="button"
                  className="px-2 py-1 text-xs rounded hover:bg-background"
                  onClick={() => document.execCommand("underline")}
                  disabled={disabled}
                >
                  <u>U</u>
                </button>
                <div className="w-px h-4 bg-border mx-1" />
                <button
                  type="button"
                  className="px-2 py-1 text-xs rounded hover:bg-background"
                  onClick={() => document.execCommand("insertUnorderedList")}
                  disabled={disabled}
                >
                  • List
                </button>
              </div>
            </div>
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
