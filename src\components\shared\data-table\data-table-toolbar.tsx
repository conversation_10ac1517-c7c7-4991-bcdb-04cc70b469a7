"use client";

import { Table } from "@tanstack/react-table";
import { X } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DataTableViewOptions } from "./data-table-view-options";

import { DataTableFacetedFilter } from "./data-table-faceted-filter";

// You'll need to import these from your data file or pass them as props
// import { priorities, statuses } from "../data/data";

interface FilterOption {
  label: string;
  value: string;
  icon?: React.ComponentType<{ className?: string }>;
}

interface FilterConfig {
  columnId: string;
  title: string;
  options: FilterOption[];
}

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  // Dynamic filter configuration
  filterConfigs?: FilterConfig[];
  // Legacy support (deprecated - use filterConfigs instead)
  filterOptions?: {
    statuses?: FilterOption[];
    priorities?: FilterOption[];
  };
  searchColumn?: string;
  searchPlaceholder?: string;
}

export function DataTableToolbar<TData>({
  table,
  filterConfigs = [],
  filterOptions, // Legacy support
  searchColumn = "title",
  searchPlaceholder = "Filter...",
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0;

  // Combine new filterConfigs with legacy filterOptions for backward compatibility
  const allFilterConfigs = [
    ...filterConfigs,
    // Legacy support - convert old format to new format
    ...(filterOptions?.statuses
      ? [
          {
            columnId: "status",
            title: "Status",
            options: filterOptions.statuses,
          },
        ]
      : []),
    ...(filterOptions?.priorities
      ? [
          {
            columnId: "priority",
            title: "Priority",
            options: filterOptions.priorities,
          },
        ]
      : []),
  ];

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-col lg:flex-row flex-1 lg:items-center gap-2">
        <Input
          placeholder={searchPlaceholder}
          value={
            (table.getColumn(searchColumn)?.getFilterValue() as string) ?? ""
          }
          onChange={(event) =>
            table.getColumn(searchColumn)?.setFilterValue(event.target.value)
          }
          className="h-8 w-full lg:w-[250px]"
        />

        {/* Dynamic filters */}
        <ul className="flex flex-wrap gap-2">
          {allFilterConfigs.map((filterConfig) => {
            const column = table.getColumn(filterConfig.columnId);
            return column ? (
              <DataTableFacetedFilter
                key={filterConfig.columnId}
                column={column}
                title={filterConfig.title}
                options={filterConfig.options}
              />
            ) : null;
          })}
        </ul>

        {isFiltered && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => table.resetColumnFilters()}
          >
            Reset
            <X />
          </Button>
        )}
      </div>
      <div className="flex items-center gap-2">
        <DataTableViewOptions table={table} />
      </div>
    </div>
  );
}
