import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import { But<PERSON> } from "@/components/ui/button";
import { Actions, Resources } from "@/lib/permissions";
import { Plus } from "lucide-react";
import PageWrapper from "../../_components/layouts/PageWrapper";
import { CustomSheet } from "@/components/shared/CustomSheet";
import { withResourceAccess } from "@/components/shared/page-gurad";
import DesignationTable from "./table";

const breadcrumbItems = [
  { label: "Home", href: "/" },
  { label: "Staffs", href: "/staffs" },
  { label: "Designations" },
];

async function Designations({
  params,
}: {
  params: Promise<Record<string, string>>;
}) {
  const school = await params;
  const renderButton = () => {
    return (
      <ClientRoleGuard
        resource={Resources.DESIGNATIONS}
        action={Actions.CREATE}
      >
        <CustomSheet
          title="Add Designation"
          trigger={
            <Button>
              <Plus /> Add Designation
            </Button>
          }
        >
          <div>Add Designations Form</div>
        </CustomSheet>
      </ClientRoleGuard>
    );
  };

  return (
    <PageWrapper
      pgTitle="Manage Designations"
      pgDescription="Manage designation records and information"
      breadcrumbItems={breadcrumbItems}
      headerButton={renderButton()}
      school={school.school}
    >
      <DesignationTable />
    </PageWrapper>
  );
}

export default withResourceAccess(Designations, {
  resource: Resources.DESIGNATIONS,
});
