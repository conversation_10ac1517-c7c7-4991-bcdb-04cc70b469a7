{"name": "kingdom_sis_web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "biome check", "format": "biome format --write", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "prisma db seed", "db:up": "docker-compose up -d postgres", "db:down": "docker-compose down", "db:logs": "docker-compose logs postgres", "db:reset": "docker-compose down -v && docker-compose up -d postgres", "db:backup": "docker exec kingdom_sis_postgres pg_dump -U kingdom_user kingdom_sis > backup_$(date +%Y%m%d_%H%M%S).sql", "db:health": "curl http://localhost:3000/api/health/database"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@prisma/client": "6.15.0", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.543.0", "next": "15.5.2", "next-auth": "5.0.0-beta.29", "next-themes": "^0.4.6", "postcss": "^8.5.6", "prisma": "^6.15.0", "react": "19.1.0", "react-day-picker": "^9.9.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^4.1.7"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@faker-js/faker": "^9.9.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.8", "typescript": "^5"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}}