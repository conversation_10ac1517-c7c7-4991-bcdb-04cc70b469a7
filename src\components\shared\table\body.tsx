import { RefreshCw } from "lucide-react";
import { TableRow } from "./row";

type Props = {
  data: any[];
  columns: any[];
  loading: boolean;
  enableBulkActions: boolean;
  selectedRows: Set<any>;
  onRowSelect: (rowId: any) => void;
  onRowAction?: (rowId: any, action: string) => void;
  actions: string[];
  renderCell?: (row: any, column: any) => React.ReactNode;
  emptyMessage?: string;
};

export function TableBody({
  data,
  columns,
  loading,
  enableBulkActions,
  selectedRows,
  onRowSelect,
  onRowAction,
  actions,
  renderCell,
  emptyMessage = "No data found",
}: Props) {
  const colSpan = columns.length + (enableBulkActions ? 1 : 0) + 1; // +1 for actions column

  if (loading) {
    return (
      <tbody className="bg-white divide-y divide-gray-200">
        <tr>
          <td colSpan={colSpan} className="px-6 py-12 text-center">
            <div className="flex items-center justify-center">
              <RefreshCw className="w-6 h-6 text-gray-400 animate-spin mr-2" />
              Loading...
            </div>
          </td>
        </tr>
      </tbody>
    );
  }

  if (data.length === 0) {
    return (
      <tbody className="bg-white divide-y divide-gray-200">
        <tr>
          <td
            colSpan={colSpan}
            className="px-6 py-12 text-center text-gray-500"
          >
            {emptyMessage}
          </td>
        </tr>
      </tbody>
    );
  }

  return (
    <tbody className="bg-white divide-y divide-gray-200">
      {data.map((row) => (
        <TableRow
          key={row.id}
          row={row}
          columns={columns}
          enableBulkActions={enableBulkActions}
          isSelected={selectedRows.has(row.id)}
          onSelect={onRowSelect}
          onAction={onRowAction}
          actions={actions}
          renderCell={renderCell}
        />
      ))}
    </tbody>
  );
}
