import {
  requireAuth,
  requireSchoolAccess,
  requirePermission,
} from "@/lib/server/auth";
import { Resources, Actions } from "@/lib/permissions";
import { Context } from "@/lib/types";

interface PageGuardOptions {
  requireAuth?: boolean;
  requireSchool?: boolean;
  resource?: string;
  action?: string;
  context?: Context;
  redirectPath?: string;
}

interface PageProps {
  params: Promise<Record<string, string>>;
  searchParams: Promise<Record<string, string>>;
}

type Page<P = {}> = (
  props: P
) => Promise<React.JSX.Element> | React.JSX.Element;

async function runGuards(
  options: PageGuardOptions = {
    requireAuth: true,
    requireSchool: true,
    action: Actions.READ,
  },
  props: PageProps
) {
  const [params, searchParams] = await Promise.all([
    props.params,
    props.searchParams,
  ]);
  const schoolSlug = params?.school;

  let user: any = null;
  if (options.requireAuth !== false) {
    user =
      options.requireSchool && schoolSlug
        ? await requireSchoolAccess(schoolSlug, options.redirectPath)
        : await requireAuth(schoolSlug, options.redirectPath);

    const context =
      options.context?.own === "true" ? { userId: user.id } : options.context;

    if (options.resource && user) {
      await requirePermission(
        user,
        options.resource,
        options.action || Actions.READ,
        context,
        options.redirectPath
      );
    }
  }
  return user;
}

/**
 * Higher-order function for protecting Next.js App Router pages.
 * Works with server components and async auth checks.
 */
export function createPageGuard<
  TProps extends {
    params: Promise<Record<string, string>>;
    searchParams: Promise<Record<string, string>>;
  }
>(options: PageGuardOptions = {}) {
  return function withPageGuard(
    PageComponent: (
      props: TProps
    ) => Promise<React.JSX.Element> | React.JSX.Element
  ) {
    // GuardedPage is a server component
    const GuardedPage = async (props: TProps): Promise<React.JSX.Element> => {
      await runGuards(options, props);

      // 3. Render the wrapped page
      return <PageComponent {...props} />;
    };

    return GuardedPage;
  };
}

/**
 * Guard for any resource/action combo
 */
export function withResourceAccess<
  P extends {
    params: Promise<Record<string, string>>;
    searchParams: Promise<Record<string, string>>;
  }
>(PageComponent: Page<P>, options: PageGuardOptions = {}): Page<P> {
  return async (props: P) => {
    await runGuards(options, props);
    return <PageComponent {...props} />;
  };
}

/**
 * Preset guards
 */
export const withAuth = createPageGuard({ requireAuth: true });

export const withSchoolAccess = createPageGuard({
  requireAuth: true,
  requireSchool: true,
});

export const withStudentsAccess = createPageGuard({
  requireAuth: true,
  requireSchool: true,
  resource: Resources.STUDENTS,
  action: Actions.READ,
});

export const withTeachersAccess = createPageGuard({
  requireAuth: true,
  requireSchool: true,
  resource: Resources.TEACHERS,
  action: Actions.READ,
});

export const withAdminAccess = createPageGuard({
  requireAuth: true,
  requireSchool: true,
  resource: Resources.SETTINGS,
  action: Actions.MANAGE,
});
