import {
  Trash2,
  Edit,
  Mail,
  Download,
  Archive,
  UserCheck,
  UserX,
  FileText,
  Send,
} from "lucide-react";
import { toast } from "sonner";

// Define the bulk actions for your staff management system
export const staffBulkActions = [
  {
    key: "activate",
    label: "Activate Staffs",
    icon: <UserCheck className="h-4 w-4" />,
    variant: "default" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Activating staffs:", selectedRows);
      // Here you would make an API call to activate the staffs
      // Example: await activateStaffs(selectedRows.map(row => row.id));
      toast.success(`Activated ${selectedRows.length} staffs`);
    },
  },
  {
    key: "send-notification",
    label: "Send Notification",
    icon: <Send className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Sending notification to staffs:", selectedRows);
      // Example: await sendNotification(selectedRows.map(row => row.id), notificationData);
      toast.info(`Sending notification to ${selectedRows.length} staffs`);
    },
  },
  {
    key: "send-email",
    label: "Send Email",
    icon: <Mail className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Sending email to staffs:", selectedRows);
      // Example: await sendBulkEmail(selectedRows.map(row => row.email), emailData);
      toast.info(`Sending email to ${selectedRows.length} staffs`);
    },
  },
  {
    key: "generate-report",
    label: "Generate Report",
    icon: <FileText className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Generating report for staffs:", selectedRows);
      // Example: await generateStaffReport(selectedRows.map(row => row.id));
      toast.info(`Generating report for ${selectedRows.length} staffs`);
    },
  },
  {
    key: "export",
    label: "Export to CSV",
    icon: <Download className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Exporting staffs:", selectedRows);

      // Create CSV content
      const headers = [
        "Name",
        "Email",
        "Status",
        "Grade",
        "Department",
        "Enrollment Type",
      ];
      const csvContent = [
        headers.join(","),
        ...selectedRows.map((staff) =>
          [
            `"${staff.name}"`,
            `"${staff.email}"`,
            `"${staff.status}"`,
            `"${staff.grade}"`,
            `"${staff.department}"`,
            `"${staff.enrollmentType}"`,
          ].join(",")
        ),
      ].join("\n");

      // Download CSV file
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `staffs-export-${
        new Date().toISOString().split("T")[0]
      }.csv`;
      link.click();
      window.URL.revokeObjectURL(url);

      toast.success(`Exported ${selectedRows.length} staffs to CSV`);
    },
  },
  {
    key: "bulk-edit",
    label: "Bulk Edit",
    icon: <Edit className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Opening bulk edit for staffs:", selectedRows);
      // Example: openBulkEditModal(selectedRows);
      toast.info(`Opening bulk edit for ${selectedRows.length} staffs`);
    },
  },
  {
    key: "archive",
    label: "Archive Staffs",
    icon: <Archive className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Archiving staffs:", selectedRows);
      // Example: await archiveStaffs(selectedRows.map(row => row.id));
      toast.info(`Archived ${selectedRows.length} staffs`);
    },
  },
  {
    key: "deactivate",
    label: "Deactivate Staffs",
    icon: <UserX className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Deactivating staffs:", selectedRows);
      // Example: await deactivateStaffs(selectedRows.map(row => row.id));
      toast.warning(`Deactivated ${selectedRows.length} staffs`);
    },
  },
  {
    key: "delete",
    label: "Delete Staffs",
    icon: <Trash2 className="h-4 w-4" />,
    variant: "destructive" as const,
    onClick: (selectedRows: any[]) => {
      const confirmed = confirm(
        `Are you sure you want to delete ${selectedRows.length} staffs? This action cannot be undone.`
      );

      if (confirmed) {
        console.log("Deleting staffs:", selectedRows);
        // Example: await deleteStaffs(selectedRows.map(row => row.id));
        toast.error(`Deleted ${selectedRows.length} staffs`);
      }
    },
  },
];
