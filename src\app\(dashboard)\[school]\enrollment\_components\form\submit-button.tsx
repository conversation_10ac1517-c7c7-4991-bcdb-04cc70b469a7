"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>cle, ChevronRight, Loader2 } from "lucide-react";

export default function EnrolmentFormSubitButton({
  isSumitting,
  isLast = false,
}: {
  isSumitting: boolean;
  isLast?: boolean;
}) {
  return (
    <div className="flex gap-4 pt-4 items-center justify-between">
      <span className="flex gap-1 items-center text-green-500 font-semibold text-lg">
        <CheckCircle /> Submitted
      </span>
      <div className="flex gap-4">
        {/* <Button
          type="button"
          variant="outline"
          onClick={handleReset}
          disabled={form.formState.isSubmitting}
        >
          Reset Form
        </Button> */}
        <Button
          type="submit"
          disabled={isSumitting}
          className="flex-1 md:flex-none"
        >
          {isSumitting ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <>
              Submit{" "}
              {isLast ? (
                ""
              ) : (
                <>
                  and Continue <ChevronRight />
                </>
              )}
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
