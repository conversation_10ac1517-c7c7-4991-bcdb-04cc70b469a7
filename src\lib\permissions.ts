import { User } from "./types";

type UserRole = "super_admin" | "admin" | "teacher" | "student" | "parent" | "applicant" | "principal"

export enum Resources {
  DASHBOARD = 'dashboard',
  ACADEMICS = 'academics',
  DEPARTMENTS = 'departments',
  CLASSES = 'classes',
  USERS = 'users',
  SESSIONS = 'sessions',
  SUBJECTS = 'subjects',
  TIMETABLES = 'timetables',
  CERTIFICATES = 'certificates',
  GRADES = 'grades',
  ID_CARDS = 'id_cards',
  STAFFS = 'staffs',
  CONTACTS = 'contacts',
  TEACHERS = 'teachers',
  DESIGNATIONS = 'designations',
  PAYROLLS = 'payrolls',
  LEAVE = 'leave',
  STUDENTS = 'students',
  REGISTRATIONS = 'registrations',
  HEALTH_RECORDS = 'health_records',
  PROMOTIONS = 'promotions',
  LEAVE_REQUESTS = 'leave_requests',
  ATTENDANCE = 'attendance',
  REPORTS = 'reports',
  SETTINGS = 'settings',
  ROLES = 'roles',
  FEES = 'fees',
  FEE_ALLOCATIONS = 'fee_allocations',
  PAYMENT = 'payment',
  ENROLLMENT_FORM = 'enrollment_form',
  PRINTOUT = 'printout',
  HELP = 'help',
}

export enum Actions {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  MANAGE = 'manage', // Full access
  TAKE = 'take', // Take attendance
}

// Predefined system roles with permissions
export const SYSTEM_ROLES = {
  SUPER_ADMIN: {
    name: 'super_admin',
    displayName: 'Super Admin',
    description: 'Super Administrator with all permissions',
    permissions: [
      { resource: '*', action: 'manage' }, // Full access to everything
    ],
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  ADMIN: {
    name: 'admin',
    displayName: 'School Admin',
    description: 'School Administrator with limited permissions',
    permissions: [
      { resource: Resources.DASHBOARD, action: Actions.READ },
      { resource: Resources.STUDENTS, action: Actions.MANAGE },
      { resource: Resources.TEACHERS, action: Actions.MANAGE },
      { resource: Resources.CLASSES, action: Actions.MANAGE },
      { resource: Resources.GRADES, action: Actions.READ },
      { resource: Resources.ATTENDANCE, action: Actions.READ },
      { resource: Resources.REPORTS, action: Actions.READ },
      { resource: Resources.SETTINGS, action: Actions.MANAGE },
      { resource: Resources.USERS, action: Actions.MANAGE },
      { resource: Resources.ROLES, action: Actions.MANAGE },
    ],
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  PRINCIPAL: {
    name: 'principal',
    displayName: 'Principal',
    description: 'Principal with limited permissions',
    permissions: [
      { resource: Resources.DASHBOARD, action: Actions.READ },
      { resource: Resources.STUDENTS, action: Actions.MANAGE },
      { resource: Resources.TEACHERS, action: Actions.MANAGE },
      { resource: Resources.CLASSES, action: Actions.MANAGE },
      { resource: Resources.GRADES, action: Actions.READ },
      { resource: Resources.ATTENDANCE, action: Actions.READ },
      { resource: Resources.REPORTS, action: Actions.READ },
      { resource: Resources.SETTINGS, action: Actions.MANAGE },
      { resource: Resources.USERS, action: Actions.MANAGE },
      { resource: Resources.ROLES, action: Actions.MANAGE },
    ],
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  TEACHER: {
    name: 'teacher',
    displayName: 'Teacher',
    description: 'Teacher with classroom management permissions',
    permissions: [
      { resource: Resources.DASHBOARD, action: Actions.READ },
      { resource: Resources.STUDENTS, action: Actions.READ },
      { resource: Resources.CLASSES, action: Actions.READ, conditions: { ownClasses: true } },
      { resource: Resources.GRADES, action: Actions.MANAGE, conditions: { ownClasses: true } },
      { resource: Resources.ATTENDANCE, action: Actions.MANAGE, conditions: { ownClasses: true } },
      { resource: Resources.REPORTS, action: Actions.READ, conditions: { ownClasses: true } },
    ],
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  STUDENT: {
    name: 'student',
    displayName: 'Student',
    description: 'Student with limited view permissions',
    permissions: [
      { resource: Resources.DASHBOARD, action: Actions.READ },
      { resource: Resources.GRADES, action: Actions.READ, conditions: { ownRecords: true } },
      { resource: Resources.ATTENDANCE, action: Actions.READ, conditions: { ownRecords: true } },
      { resource: Resources.CLASSES, action: Actions.READ, conditions: { enrolledClasses: true } },
    ],
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  PARENT: {
    name: 'parent',
    displayName: 'Parent',
    description: 'Parent with view permissions for their children',
    permissions: [
      { resource: Resources.DASHBOARD, action: Actions.READ },
      { resource: Resources.STUDENTS, action: Actions.READ, conditions: { ownChildren: true } },
      { resource: Resources.GRADES, action: Actions.READ, conditions: { ownChildren: true } },
      { resource: Resources.ATTENDANCE, action: Actions.READ, conditions: { ownChildren: true } },
    ],
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  APPLICANT: {
    name: 'applicant',
    displayName: 'Applicant',
    description: 'Applicant with limited view permissions',
    permissions: [
      { resource: Resources.DASHBOARD, action: Actions.READ },
      { resource: Resources.ENROLLMENT_FORM, action: Actions.READ, conditions: { ownRecords: true } },
      { resource: Resources.ENROLLMENT_FORM, action: Actions.CREATE },
      { resource: Resources.ENROLLMENT_FORM, action: Actions.UPDATE, conditions: { ownRecords: true } },
      { resource: Resources.PRINTOUT, action: Actions.READ, conditions: { ownRecords: true } },
      { resource: Resources.PAYMENT, action: Actions.READ, conditions: { ownRecords: true } },
      { resource: Resources.HELP, action: Actions.READ },
    ],
    isSystemRole: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
};

export class PermissionChecker {
  private user: User;

  constructor(user: User) {
    this.user = user;
  }

  hasPermission(resource: string, action: string, context?: any): boolean {
    // Super admin has access to everything
    if (this.user.role === 'super_admin') {
      return true;
    }

    // Check user's permissions
    const hasPermission = this.user.permissions.some(permission => {
      // Check for wildcard permissions
      if (permission.resource === '*' && permission.action === 'manage') {
        return true;
      }

      // Check exact match
      if (permission.resource === resource && 
          (permission.action === action || permission.action === 'manage')) {
        
        // Check additional conditions if present
        if (permission.conditions && context) {
          return this.checkConditions(permission.conditions, context);
        }
        
        return true;
      }

      return false;
    });

    return hasPermission;
  }

  private checkConditions(conditions: any, context: any): boolean {
    // Implement condition checking logic
    // Examples:
    // - ownRecords: user can only access their own records
    // - ownChildren: parent can only access their children's records
    // - ownClasses: teacher can only access classes they teach
    
    if (conditions.ownRecords && context.userId !== this.user.id) {
      return false;
    }

    if (conditions.ownChildren && context.parentId !== this.user.id) {
      return false;
    }

    if (conditions.ownClasses && !context.teacherIds?.includes(this.user.id)) {
      return false;
    }

    return true;
  }

  canAccess(resource: string, action: string = Actions.READ, context?: any): boolean {
    return this.hasPermission(resource, action, context);
  }
}

// Helper function to check permissions
export function checkPermission(
  user: User, 
  resource: string, 
  action: string = Actions.READ, 
  context?: any
): boolean {
  const checker = new PermissionChecker(user);
  return checker.hasPermission(resource, action, context);
}