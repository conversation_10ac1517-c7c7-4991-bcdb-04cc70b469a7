"use client";

import { Button } from "@/components/ui/button";

export function BulkActionsBar({
  selectedCount,
  bulkActions,
}: {
  selectedCount: number;
  bulkActions: {
    key: string;
    label: string;
    onClick: () => void;
    variant?: "danger" | "primary";
  }[];
}) {
  if (selectedCount === 0) return null;

  return (
    <div className="mb-4 p-3 border rounded-lg">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-primary">
          {selectedCount} row(s) selected
        </span>
        <div className="flex items-center gap-2">
          {bulkActions.map((action) => (
            <Button
              key={action.key}
              onClick={() => action.onClick()}
              variant={action.variant === "danger" ? "destructive" : "default"}
            >
              {action.label}
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
}
