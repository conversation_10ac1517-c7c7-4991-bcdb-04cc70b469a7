"use client";

import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertTriangleIcon, CheckCircle, Trash2Icon } from "lucide-react";
import { toast } from "sonner";
import z from "zod";
import { useFieldArray, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import { useEffect } from "react";
import { FormTextarea } from "@/components/forms";
import EnrolmentFormSubitButton from "./submit-button";

const healthSurveySchema = z.object({
  medicalHistory: z.string().optional(),
  medicalNeeds: z.string().optional(),
});

type HealthSurveyFormData = z.infer<typeof healthSurveySchema>;

interface HealthSurveyFormProps {
  initialData?: Partial<HealthSurveyFormData>;
}

export default function HealthSurveyForm({
  initialData,
}: HealthSurveyFormProps) {
  const form = useForm<HealthSurveyFormData>({
    resolver: zod<PERSON><PERSON>olver(healthSurveySchema),
    defaultValues: {
      medicalHistory: "",
      medicalNeeds: "",
      ...initialData,
    },
  });

  useEffect(() => {
    if (initialData) {
      form.reset(initialData);
    }
  }, [initialData, form]);

  const handleSubmit = async (data: HealthSurveyFormData) => {
    try {
      console.log(data);
      // await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error("Form submission failed", {
        description: "Please check your input and try again.",
      });
    }
  };

  return (
    <Card className="w-full">
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Academic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Academic Information</h3>
              <div className="flex flex-col gap-4">
                <FormTextarea
                  form={form}
                  name="medicalHistory"
                  label="Specify your Childs medical history (seizures, asthma,
                  allergies, etc), if any"
                  placeholder="Enter medical history"
                  rows={4}
                />
                <FormTextarea
                  form={form}
                  name="medicalNeeds"
                  label="Specify your Childs medical needs/clinical diagnosis (Autism,
                  ADHD, Global Developmental Delay, Learning Definciency), if
                  any"
                  placeholder="Enter medical needs"
                  rows={4}
                />
              </div>
            </div>

            {/* Form Actions */}
            <EnrolmentFormSubitButton
              isSumitting={form.formState.isSubmitting}
            />
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
