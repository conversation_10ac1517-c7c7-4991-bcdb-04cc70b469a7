"use client";

import type React from "react";
import { createContext, useContext, useReducer, useEffect } from "react";
import type { AuthState, AuthContextType, User } from "@/lib/types";
import { authenticateUser } from "@/lib/auth";
import { checkPermission } from "@/lib/permissions";

// Auth reducer
type AuthAction =
  | { type: "LOGIN_START" }
  | { type: "LOGIN_SUCCESS"; payload: User }
  | { type: "LOGIN_FAILURE" }
  | { type: "LOGOUT" }
  | { type: "RESTORE_SESSION"; payload: User };

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case "LOGIN_START":
      return { ...state, isLoading: true };
    case "LOGIN_SUCCESS":
      return {
        user: action.payload,
        school: "kingdon",
        isAuthenticated: true,
        isLoading: false,
      };
    case "LOGIN_FAILURE":
      return {
        user: null,
        school: "kingdon",
        isAuthenticated: false,
        isLoading: false,
      };
    case "LOGOUT":
      return {
        user: null,
        school: "kingdon",
        isAuthenticated: false,
        isLoading: false,
      };
    case "RESTORE_SESSION":
      return {
        user: action.payload,
        school: "kingdon",
        isAuthenticated: true,
        isLoading: false,
      };
    default:
      return state;
  }
};

const initialState: AuthState = {
  user: null,
  school: "kingdon",
  isAuthenticated: false,
  isLoading: true,
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Restore session on mount
  useEffect(() => {
    const savedUser = localStorage.getItem("school_user");
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser);
        dispatch({ type: "RESTORE_SESSION", payload: user });
      } catch {
        localStorage.removeItem("school_user");
        dispatch({ type: "LOGIN_FAILURE" });
      }
    } else {
      dispatch({ type: "LOGIN_FAILURE" });
    }
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    dispatch({ type: "LOGIN_START" });

    try {
      const user = await authenticateUser(email, password);
      if (user) {
        localStorage.setItem("school_user", JSON.stringify(user));
        dispatch({ type: "LOGIN_SUCCESS", payload: user });
        return true;
      } else {
        dispatch({ type: "LOGIN_FAILURE" });
        return false;
      }
    } catch (error) {
      dispatch({ type: "LOGIN_FAILURE" });
      return false;
    }
  };

  const logout = () => {
    localStorage.removeItem("school_user");
    dispatch({ type: "LOGOUT" });
  };

  const hasPermission = (resource: string, action: string): boolean => {
    if (!state.user) return false;
    return checkPermission(state.user, resource, action);
  };

  const value: AuthContextType = {
    ...state,
    login,
    logout,
    hasPermission,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
