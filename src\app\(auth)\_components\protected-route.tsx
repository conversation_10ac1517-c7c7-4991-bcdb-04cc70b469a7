"use client";

import type React from "react";

import { useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
// import { getAuthorizedRoutes } from "@/lib/auth";
import { Loader2 } from "lucide-react";
import { usePermissions } from "@/hooks/use-permissions";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string[];
  requiredPermission?: { resource: string; action: string };
}

export function ProtectedRoute({
  children,
  requiredRole,
  requiredPermission,
}: ProtectedRouteProps) {
  const { user, isAuthenticated, school, isLoading, hasPermission } = useAuth();
  const { canViewDashboard } = usePermissions();
  const router = useRouter();
  const params = useParams();

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push("/login");
        return;
      }

      if (school !== params.school) {
        router.push("/login");
        return;
      }

      if (!canViewDashboard()) {
        router.push("/unauthorized");
        return;
      }

      // if (user) {
      //   // Check role-based access
      //   if (requiredRole && !requiredRole.includes(user.role)) {
      //     const authorizedRoutes = getAuthorizedRoutes(user.role);
      //     router.push(authorizedRoutes[0] || "/login");
      //     return;
      //   }

      //   // Check permission-based access
      //   if (
      //     requiredPermission &&
      //     !hasPermission(requiredPermission.resource, requiredPermission.action)
      //   ) {
      //     const authorizedRoutes = getAuthorizedRoutes(user.role);
      //     router.push(authorizedRoutes[0] || "/login");
      //     return;
      //   }
      // }
    }
  }, [
    isAuthenticated,
    isLoading,
    user,
    requiredRole,
    requiredPermission,
    router,
    hasPermission,
    school,
    params.school,
  ]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !user || !school) {
    return null;
  }

  return <>{children}</>;
}
