'use server'

import { signIn, signOut } from "@/lib/auth";

export async function signInWithCredentials({email, password}: {email: string; password: string}) {
  try {
    if (!email || !password) {
      return {  success: false, error: "Please fill all the required fields" }
    }

    await signIn('credentials', {email, password, redirect: false})
    return { success: true, message: "Login successful"}
  } catch (error) {
    console.log(error);
    return { success: false, error: "Invalid login credentials."}
  }
}

export default async function logOut() {
  await signOut({redirect: true, redirectTo: '/login'});
}