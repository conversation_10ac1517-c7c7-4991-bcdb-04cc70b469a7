import { BarChart2, BookOpen, Building, CalendarRange, ChartAreaIcon, ChartColumn, DollarSign, Dot, File, FileCode2, FileMinusIcon, GraduationCap, Heart, HelpCircle, LayoutDashboard, MessageCircle, NotebookPen, Printer, Settings, User2, Users2 } from "lucide-react";
import { ACTIONS, ATTENDANCE_ACTIONS, createNavigationPermissions, STANDARD_ACTIONS, VIEW_ONLY } from "./permissions";

export interface NavigationItem {
  id: string;
  label: string;
  icon: React.ElementType<any, keyof React.JSX.IntrinsicElements>;
  href: string;
  resource: string;
  children?: NavigationItem[];
  badge?: string;
  isExact?: boolean;
}

export const NAVIGATION_CONFIG: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    href: '/dashboard',
    resource: 'dashboard', // Everyone can access dashboard
    icon: LayoutDashboard,
  },
  {
    id: 'academic',
    label: 'Academic',
    icon: File,
    href: '/academic',
    resource: 'academic',
    children: [
      {
        id: 'department',
        label: 'Department',
        icon: Dot,
        href: '/academics/departments',
        resource: 'departments',
        isExact: true,
      },
      {
        id: 'session',
        label: 'Session',
        icon: Dot,
        href: '/academics/sessions',
        permissions: createNavigationPermissions('session', STANDARD_ACTIONS),
      },
      {
        id: 'subject',
        label: 'Subject',
        icon: Dot,
        href: '/academics/subjects',
        permissions: createNavigationPermissions('subject', STANDARD_ACTIONS),
      },
      {
        id: 'time-table',
        label: 'Time Table',
        icon: Dot,
        href: '/academics/time-tables',
        permissions: createNavigationPermissions('timetable', STANDARD_ACTIONS),
      },
      {
        id: 'certificate',
        label: 'Certificate',
        icon: Dot,
        href: '/academics/certificates',
        permissions: createNavigationPermissions('certificate', STANDARD_ACTIONS),
      },
      {
        id: 'id-card',
        label: 'ID Card',
        icon: Dot,
        href: '/academics/id-cards',
        permissions: createNavigationPermissions('id_card', STANDARD_ACTIONS),
      },
    ],
  },
  {
    id: 'staff',
    label: 'Staff',
    icon: Users2,
    href: '/staff',
    permissions: createNavigationPermissions('staff', VIEW_ONLY),
    children: [
      {
        id: 'staff',
        label: 'Staffs',
        icon: Dot,
        href: '/staffs',
        permissions: createNavigationPermissions('staff', STANDARD_ACTIONS),
        isExact: true,
      },
      {
        id: 'department',
        label: 'Department',
        icon: Dot,
        href: '/staffs/departments',
        permissions: createNavigationPermissions('department', [...STANDARD_ACTIONS, ACTIONS.VIEW_ALL]),
      },
      {
        id: 'designation',
        label: 'Designation',
        icon: Dot,
        href: '/staffs/designations',
        permissions: createNavigationPermissions('designation', STANDARD_ACTIONS),
      },
      {
        id: 'attendance',
        label: 'Attendance',
        icon: Dot,
        href: '/staffs/attendances',
        permissions: createNavigationPermissions('attendance', ATTENDANCE_ACTIONS),
      },
      {
        id: 'leave',
        label: 'Leave',
        icon: Dot,
        href: '/staffs/leaves',
        permissions: createNavigationPermissions('leave', STANDARD_ACTIONS),
      },
      {
        id: 'payroll',
        label: 'Payroll',
        icon: Dot,
        href: '/staffs/payrolls',
        permissions: createNavigationPermissions('payroll', STANDARD_ACTIONS),
      },
    ],
  },
  {
    id: 'student',
    label: 'Student',
    icon: Users2,
    href: '/students',
    permissions: createNavigationPermissions('student', VIEW_ONLY),
    children: [
      {
        id: 'student',
        label: 'Students',
        icon: Dot,
        href: '/students',
        permissions: createNavigationPermissions('student', STANDARD_ACTIONS),
        isExact: true,
      },
      {
        id: 'registration',
        label: 'Registration',
        icon: Dot,
        href: '/students/registrations',
        permissions: createNavigationPermissions('registration', STANDARD_ACTIONS),
        isExact: true,
      },
      {
        id: 'health-record',
        label: 'Health Record',
        icon: Dot,
        href: '/students/health-records',
        permissions: createNavigationPermissions('health_record', STANDARD_ACTIONS),
      },
      {
        id: 'attendance',
        label: 'Sttendance',
        icon: Dot,
        href: '/students/attendances',
        permissions: createNavigationPermissions('attendance', ATTENDANCE_ACTIONS),
      },
      {
        id: 'promotion',
        label: 'Promotion',
        icon: Dot,
        href: '/students/promotions',
        permissions: createNavigationPermissions('promotion', STANDARD_ACTIONS),
      },
      {
        id: 'fee-allocation',
        label: 'Fee Allocation',
        icon: Dot,
        href: '/students/fee-allocations',
        permissions: createNavigationPermissions('fee_allocation', STANDARD_ACTIONS),
      },
      {
        id: 'leave-request',
        label: 'Leave Request',
        icon: Dot,
        href: '/students/leave-requests',
        permissions: createNavigationPermissions('leave_request', STANDARD_ACTIONS),
      },
      {
        id: 'report',
        label: 'Report',
        icon: Dot,
        href: '/students/reports',
        permissions: createNavigationPermissions('report', STANDARD_ACTIONS),
      },
    ],
  },
  
  // enrollment
  {
    id: 'form',
    label: 'Admission Form',
    icon: FileCode2,
    href: '/enrollment/form',
    permissions: createNavigationPermissions('enrollment_form', VIEW_ONLY),
    children: [
      {
        id: 'personal',
        label: 'Personal Information',
        icon: User2,
        href: '/enrollment/form/personal',
        permissions: createNavigationPermissions('enrollment_form', STANDARD_ACTIONS),
      },
      {
        id: 'education',
        label: 'Educational Background',
        icon: BookOpen,
        href: '/enrollment/form/education',
        permissions: createNavigationPermissions('enrollment_form', STANDARD_ACTIONS),
      },
      {
        id: 'family',
        label: 'Family Background',
        icon: Users2,
        href: '/enrollment/form/family',
        permissions: createNavigationPermissions('enrollment_form', STANDARD_ACTIONS),
      },
      {
        id: 'health',
        label: 'Health Survey',
        icon: Heart,
        href: '/enrollment/form/health',
        permissions: createNavigationPermissions('enrollment_form', STANDARD_ACTIONS),
      },
      {
        id: 'document',
        label: 'Documentation',
        icon: FileMinusIcon,
        href: '/enrollment/form/document',
        permissions: createNavigationPermissions('enrollment_form', STANDARD_ACTIONS),
      },
      {
        id: 'other',
        label: 'Other Information',
        icon: BarChart2,
        href: '/enrollment/form/other',
        permissions: createNavigationPermissions('enrollment_form', STANDARD_ACTIONS),
      },
    ],
  },
  {
    id: 'printout',
    label: 'Printout',
    icon: Printer,
    href: '/printout',
    permissions: createNavigationPermissions('admission_printout', VIEW_ONLY),
    children: [
      {
        id: 'exam',
        label: 'Exam Information',
        icon: User2,
        href: '/enrollment/printout/exam-info',
        permissions: createNavigationPermissions('admission_printout', VIEW_ONLY),
      },
      {
        id: 'student',
        label: 'Student Information',
        icon: BookOpen,
        href: '/enrollment/printout/student-info',
        permissions: createNavigationPermissions('admission_printout', VIEW_ONLY),
      },
    ],
  },
  {
    id: 'payment',
    label: 'Payment',
    icon: DollarSign,
    href: '/enrollment/payment',
    permissions: createNavigationPermissions('payment', STANDARD_ACTIONS),
  },
  {
    id: 'help',
    label: 'Help and Support',
    icon: HelpCircle,
    href: '/enrollment/help',
    permissions: createNavigationPermissions('help', STANDARD_ACTIONS),
  },

  {
    id: 'role',
    label: 'Roles',
    icon: User2,
    href: '/roles',
    permissions: createNavigationPermissions('role', VIEW_ONLY),
    children: [
      {
        id: 'role',
        label: 'Role',
        icon: Dot,
        href: '/roles',
        permissions: createNavigationPermissions('role', STANDARD_ACTIONS),
        isExact: true,
      },
      {
        id: 'permission',
        label: 'Permission',
        icon: Dot,
        href: '/roles/permissions',
        permissions: createNavigationPermissions('permission', STANDARD_ACTIONS),
      },
    ],
  },
  {
    id: 'classes',
    label: 'Classes',
    icon: Building,
    href: '/classes',
    permissions: createNavigationPermissions('class', VIEW_ONLY),
    children: [
      {
        id: 'classes_list',
        label: 'All Classes',
        icon: Dot,
        href: '/classes',
        permissions: createNavigationPermissions('class', STANDARD_ACTIONS),
        isExact: true,
      },
      {
        id: 'classes_add',
        label: 'Create Class',
        icon: Dot,
        href: '/classes/add',
        permissions: createNavigationPermissions('class', [ACTIONS.CREATE]),
      },
    ],
  },
  {
    id: 'grades',
    label: 'Grades',
    icon: GraduationCap,
    href: '/grades',
    permissions: ['grades.view_all', 'grades.view_own', 'grades.view_children', 'grades.view_class'],
  },
  {
    id: 'user',
    label: 'Users',
    icon: User2,
    href: '/users',
    permissions: createNavigationPermissions('user', VIEW_ONLY),
  },
  {
    id: 'attendance',
    label: 'Attendance',
    icon: CalendarRange,
    href: '/attendance',
    permissions: ['attendance.view_all', 'attendance.view_own', 'attendance.view_children', 'attendance.view_class'],
  },
  {
    id: 'assignments',
    label: 'Assignments',
    icon: NotebookPen,
    href: '/assignments',
    permissions: ['assignments.view_all', 'assignments.view_own', 'assignments.view_children'],
  },
  {
    id: 'communications',
    label: 'Messages',
    icon: MessageCircle,
    href: '/communications',
    permissions: ['communication.view_all', 'communication.send_parents', 'communication.send_teachers'],
  },
  {
    id: 'fees',
    label: 'Fees',
    icon: ChartColumn,
    href: '/fees',
    permissions: createNavigationPermissions('fees', STANDARD_ACTIONS),
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: ChartAreaIcon,
    href: '/reports',
    permissions: ['reports.view', 'reports.generate'],
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    href: '/settings',
    permissions: ['settings.manage'],
    children: [
      {
        id: 'settings_school',
        label: 'School Settings',
        icon: Dot,
        href: '/settings/school',
        permissions: ['settings.manage'],
      },
      {
        id: 'settings_roles',
        label: 'Manage Roles',
        icon: Dot,
        href: '/settings/roles',
        permissions: ['roles.manage'],
      },
      {
        id: 'settings_users',
        label: 'Manage Users',
        icon: Dot,
        href: '/settings/users',
        permissions: ['users.manage'],
      },
    ],
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: User2,
    href: '/profile',
    permissions: [], // Everyone can access their profile
  },
];

export function getFilteredNavigation(userPermissions: string[]): NavigationItem[] {
  return NAVIGATION_CONFIG.filter(item => {
    // If no permissions required, show to everyone
    if (item.permissions.length === 0) return true;
    
    // Check if user has at least one required permission
    return item.permissions.some(permission => userPermissions.includes(permission));
  }).map(item => ({
    ...item,
    children: item.children?.filter(child => {
      if (child.permissions.length === 0) return true;
      return child.permissions.some(permission => userPermissions.includes(permission));
    }),
  }));
}
