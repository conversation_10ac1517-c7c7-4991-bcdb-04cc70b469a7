"use client";

import { useState } from "react";
import { StudentForm } from "@/components/student-form";
import type { StudentFormData } from "@/lib/validations/student";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Edit, Plus, Trash2, Eye } from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";

// Mock data for demonstration
const mockStudents: StudentFormData[] = [
  {
    id: "1",
    firstName: "<PERSON>",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "(*************",
    dateOfBirth: new Date("2008-05-15"),
    gender: "male",
    grade: "grade-9",
    subjects: ["mathematics", "science", "english"],
    address: "123 Main St, Anytown, ST 12345",
    emergencyContact: "(*************",
    hasScholarship: true,
    scholarshipType: "academic",
    notes: "Excellent student with strong academic performance.",
  },
];

export default function CreateStudentPsge() {
  const [students, setStudents] = useState<StudentFormData[]>(mockStudents);
  const [editingStudent, setEditingStudent] = useState<StudentFormData | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);

  const handleSubmit = async (data: StudentFormData) => {
    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      if (editingStudent) {
        // Update existing student
        setStudents((prev) =>
          prev.map((student) =>
            student.id === editingStudent.id
              ? { ...data, id: editingStudent.id }
              : student,
          ),
        );
        setEditingStudent(null);
        toast.success("Student updated successfully!", {
          description: `${data.firstName} ${data.lastName}'s information has been updated.`,
        });
      } else {
        // Add new student
        const newStudent = { ...data, id: Date.now().toString() };
        setStudents((prev) => [...prev, newStudent]);
        toast.success("Student added successfully!", {
          description: `${data.firstName} ${data.lastName} has been added to the system.`,
        });
      }

      setShowForm(false);
    } catch (error) {
      toast.error("Something went wrong!", {
        description:
          "Please try again or contact support if the problem persists.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (student: StudentFormData) => {
    setEditingStudent(student);
    setShowForm(true);
    toast.info("Editing student", {
      description: `You are now editing ${student.firstName} ${student.lastName}'s information.`,
    });
  };

  const handleDelete = (studentId: string) => {
    const student = students.find((s) => s.id === studentId);
    setStudents((prev) => prev.filter((student) => student.id !== studentId));
    if (student) {
      toast.success("Student removed", {
        description: `${student.firstName} ${student.lastName} has been removed from the system.`,
        action: {
          label: "Undo",
          onClick: () => {
            setStudents((prev) => [...prev, student]);
            toast.success("Student restored", {
              description: `${student.firstName} ${student.lastName} has been restored.`,
            });
          },
        },
      });
    }
  };

  const handleAddNew = () => {
    setEditingStudent(null);
    setShowForm(true);
    toast.info("Adding new student", {
      description:
        "Fill out the form below to add a new student to the system.",
    });
  };

  const handleCancel = () => {
    setEditingStudent(null);
    setShowForm(false);
    toast.info("Form cancelled", {
      description: "No changes were saved.",
    });
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">School Management System</h1>
          <p className="text-muted-foreground">
            Manage student information with reusable form components
          </p>
        </div>
        <div className="flex gap-2">
          <Link href="/school/showcase">
            <Button
              variant="outline"
              className="flex items-center gap-2 bg-transparent"
            >
              <Eye className="h-4 w-4" />
              View Components
            </Button>
          </Link>
          {!showForm && (
            <Button onClick={handleAddNew} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Student
            </Button>
          )}
        </div>
      </div>

      {showForm ? (
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={handleCancel}>
              ← Back to List
            </Button>
          </div>
          <StudentForm
            initialData={editingStudent || undefined}
            onSubmit={handleSubmit}
            isLoading={isLoading}
            mode={editingStudent ? "edit" : "create"}
          />
        </div>
      ) : (
        <div className="space-y-6">
          <div className="grid gap-6">
            {students.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <p className="text-muted-foreground mb-4">
                    No students found
                  </p>
                  <Button onClick={handleAddNew}>Add First Student</Button>
                </CardContent>
              </Card>
            ) : (
              students.map((student) => (
                <Card key={student.id}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          {student.firstName} {student.lastName}
                          {student.hasScholarship && (
                            <Badge variant="secondary">Scholarship</Badge>
                          )}
                        </CardTitle>
                        <CardDescription>{student.email}</CardDescription>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(student)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(student.id!)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Phone:</span>{" "}
                        {student.phone}
                      </div>
                      <div>
                        <span className="font-medium">Grade:</span>{" "}
                        {student.grade}
                      </div>
                      <div>
                        <span className="font-medium">Gender:</span>{" "}
                        {student.gender}
                      </div>
                      <div className="md:col-span-2 lg:col-span-3">
                        <span className="font-medium">Subjects:</span>{" "}
                        <div className="flex flex-wrap gap-1 mt-1">
                          {student.subjects.map((subject: string) => (
                            <Badge
                              key={subject}
                              variant="outline"
                              className="text-xs"
                            >
                              {subject}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      {student.notes && (
                        <div className="md:col-span-2 lg:col-span-3">
                          <span className="font-medium">Notes:</span>{" "}
                          {student.notes}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}
