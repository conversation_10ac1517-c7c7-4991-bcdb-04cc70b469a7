import type React from "react";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import AppSidebar from "./_components/navigation/app-sidebar";
import { AppHeader } from "./_components/navigation/app-header";
import { notFound, redirect } from "next/navigation";
import { auth } from "@/lib/auth";
import { Resources } from "@/lib/permissions";
import { SessionProvider } from "next-auth/react";

export default async function AppLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ school: string }>;
}) {
  const { school: schoolSlug } = await params;
  const session = await auth();

  if (!session || !session.user) {
    redirect("/login");
  }

  if (session.user.school !== schoolSlug) {
    notFound();
  }

  if (
    !session.user.permissions.some(
      (p) =>
        (p.resource === Resources.DASHBOARD || p.resource === "*") &&
        (p.action === "read" || p.action === "manage")
    )
  ) {
    redirect("/unauthorized?reason=dashboard_access");
  }

  return (
    <SessionProvider>
      <SidebarProvider>
        <AppSidebar schoolSlug={schoolSlug} user={session.user} />

        <SidebarInset>
          <AppHeader user={session.user} />
          <div className="flex-1 overflow-y-auto p-6">{children}</div>
        </SidebarInset>
      </SidebarProvider>
    </SessionProvider>
  );
}
