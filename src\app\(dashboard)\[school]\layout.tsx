import type React from "react";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { ProtectedRoute } from "@/app/(auth)/_components/protected-route";
import AppSidebar from "./_components/navigation/app-sidebar";
import { AppHeader } from "./_components/navigation/app-header";

export default async function AppLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ school: string }>;
}) {
  const requiredRole = ["admin", "teacher"];
  const { school } = await params;

  return (
    <ProtectedRoute requiredRole={requiredRole}>
      <SidebarProvider>
        <AppSidebar schoolSlug={school} />

        <SidebarInset>
          <AppHeader />
          <div className="flex-1 overflow-y-auto p-6">{children}</div>
        </SidebarInset>
      </SidebarProvider>
    </ProtectedRoute>
  );
}
