"use client";

import type React from "react";

import { useMemo } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ChevronRight, Loader2, LogOut } from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from "@/components/ui/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Separator } from "@/components/ui/separator";
import Logo from "@/components/shared/logo";
import { NAVIGATION_CONFIG } from "@/data/navigation";
import { usePermissions } from "@/hooks/use-permissions";
import logOut from "@/lib/server/users/users.action";
import { Session } from "next-auth";

function AppSidebar({
  schoolSlug,
  user,
}: {
  schoolSlug: string;
  user: Session["user"];
}) {
  const pathname = usePathname();
  const { hasPermission, isLoading } = usePermissions(user);

  const filteredNavItems = NAVIGATION_CONFIG.filter((item) =>
    hasPermission(item.resource)
  );

  // Memoize the active link and open collapsibles to prevent unnecessary re-renders
  const { activeLinks, openCollapsibles } = useMemo(() => {
    const active = new Set<string>();
    const open = new Set<string>();

    // Check which links should be active and which collapsibles should be open
    filteredNavItems.forEach((link) => {
      // Check if the current path matches the main link
      if (pathname === `/${schoolSlug}${link.href}`) {
        active.add(link.href);
      }

      // Check if any sub-item matches the current path
      if (link.children) {
        const hasActiveChild = link.children.some((item) => {
          const isActive = pathname === `/${schoolSlug}${item.href}`;
          if (isActive) {
            active.add(item.href);
            // If a child is active, the parent collapsible should be open
            open.add(link.label);
          }
          return isActive;
        });

        // If the main path starts with the link URL (for partial matches)
        if (
          !hasActiveChild &&
          pathname.startsWith(`/${schoolSlug}${link.href}`) &&
          link.href !== "/"
        ) {
          open.add(link.label);
        }
      }
    });

    return { activeLinks: active, openCollapsibles: open };
  }, [pathname]);

  return (
    <Sidebar collapsible="icon" className="overflow-hidden">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild className="gap-0">
              <Logo />
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Platform</SidebarGroupLabel>
          <SidebarMenu>
            {isLoading || !filteredNavItems ? (
              <Loader2 className="animate-spin p-6" />
            ) : (
              filteredNavItems.map((item) => {
                if (item.children) {
                  return (
                    <Collapsible
                      key={item.label}
                      asChild
                      defaultOpen={openCollapsibles.has(item.label)}
                      className="group/collapsible"
                    >
                      <SidebarMenuItem>
                        <CollapsibleTrigger asChild>
                          <SidebarMenuButton
                            tooltip={item.label}
                            className="h-10 px-2.5"
                            isActive={activeLinks.has(item.href)}
                          >
                            <item.icon className="h-4 w-4" />
                            <span>{item.label}</span>
                            <ChevronRight className="ml-auto h-4 w-4 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                          </SidebarMenuButton>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                          <SidebarMenuSub>
                            {item.children?.map((subItem) => (
                              <SidebarMenuSubItem key={subItem.label}>
                                <SidebarMenuSubButton
                                  asChild
                                  className="h-8"
                                  isActive={activeLinks.has(subItem.href)}
                                >
                                  <Link href={`/${schoolSlug}${subItem.href}`}>
                                    <span>{subItem.label}</span>
                                  </Link>
                                </SidebarMenuSubButton>
                              </SidebarMenuSubItem>
                            ))}
                          </SidebarMenuSub>
                        </CollapsibleContent>
                      </SidebarMenuItem>
                    </Collapsible>
                  );
                }

                return (
                  <SidebarMenuItem key={item.label}>
                    <SidebarMenuButton
                      tooltip={item.label}
                      className="h-10 px-2.5"
                      asChild
                      isActive={activeLinks.has(item.href)}
                    >
                      <Link href={`/${schoolSlug}${item.href}`}>
                        <item.icon className="h-4 w-4" />
                        <span>{item.label}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })
            )}

            <Separator className="my-4" />

            {/* Sign out button */}
            <SidebarMenuItem>
              <SidebarMenuButton
                tooltip="Sign out"
                className="h-10 px-2.5 text-red-500 hover:bg-red-500/10 hover:text-red-600"
                asChild
              >
                <button
                  onClick={async () => {
                    await logOut();
                  }}
                >
                  <LogOut className="h-4 w-4" />
                  <span>Sign out</span>
                </button>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}

export default AppSidebar;
