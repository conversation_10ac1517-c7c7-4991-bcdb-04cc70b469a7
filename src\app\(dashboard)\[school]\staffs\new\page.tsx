import { Actions, Resources } from "@/lib/permissions";
import { withResourceAccess } from "@/components/shared/page-gurad";
import { StaffForm } from "../staff-form";
import PageWrapper from "../../_components/layouts/PageWrapper";

const breadcrumbItems = [
  { label: "Home", href: "/" },
  { label: "Staff", href: "/staffs" },
  { label: "New" },
];

function StaffPage() {
  return (
    <PageWrapper
      pgTitle="Add Staff"
      pgDescription="Add a new staff member"
      breadcrumbItems={breadcrumbItems}
      school="kingdom"
    >
      <StaffForm />
    </PageWrapper>
  );
}

export default withResourceAccess(StaffPage, {
  resource: Resources.STAFFS,
  action: Actions.MANAGE,
});
