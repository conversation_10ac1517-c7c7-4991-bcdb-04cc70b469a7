import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import { withResourceAccess } from "@/components/shared/page-gurad";
import { Button } from "@/components/ui/button";
import { Actions, Resources } from "@/lib/permissions";
import { Loader2 } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

function PayentPage() {
  const institutionName = "Kingdom School College";
  const isApproved = true;
  return (
    <div className="w-full min-h-[80%] flex justify-center items-center">
      <div className="bg-accent text-accent-foreground max-w-4xl mx-auto p-8 space-y-8 flex flex-col rounded-md order-2 md:order-1">
        {isApproved ? (
          <>
            <header className="">
              <h3 className="text-lg font-medium">
                Admission purchase payment
              </h3>
              <p className="text-2xl font-bold text-primary">P1,300</p>
            </header>

            <div>
              <h4 className="font-medium">Payment for:</h4>
              <p>Admission form purchase</p>
            </div>

            <div className="">
              <h4 className="font-medium">Please note:</h4>
              <p className="text-muted-foreground">
                Your admission will be forfeited if you don&apos;t make this
                payment as your registration will not be granted by the system.
              </p>
            </div>

            <ClientRoleGuard resource={Resources.PAYMENT} action={Actions.READ}>
              <Button asChild size="lg">
                <Link href="/school/sis/payment/paymentStatus">
                  Proceed to payment
                </Link>
              </Button>
            </ClientRoleGuard>
          </>
        ) : !isApproved ? (
          <div className="flex flex-col gap-8 items-center">
            <header className="font-semibold text-3xl text-primary">
              Come back and check again!
            </header>

            <Image
              src={"/images/placeholder.svg"}
              alt="Adio I am waiting for this icon right here"
              width={100}
              height={100}
            />

            <p className="text-center text-sm text-muted-foreground">
              We apologize to inform you that your application has not been
              accepted/verified by the school at this time. Please review your
              application details and try again. For further assistance and
              clarification, you can contact our{" "}
              <Link
                href="/school/enrollment/help"
                className="text-primary underline"
              >
                help page
              </Link>
              . Our team is readily available to provide any additional
              information you may need. Thank you for considering{" "}
              {institutionName}, and we encourage you to explore other
              opportunities.
            </p>
          </div>
        ) : (
          <h2 className="h3-medium text-center flex justify-center items-center size-full">
            <Loader2 className="animate-spin h-8 w-8" />
          </h2>
        )}
      </div>
    </div>
  );
}

export default withResourceAccess(PayentPage, {
  resource: Resources.PAYMENT,
  context: { own: "true" },
});
