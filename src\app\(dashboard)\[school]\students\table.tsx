"use client";

import { DataTable } from "@/components/shared/data-table/data-table";
import { getStudents, Students } from "@/lib/server/students/students.list";
import {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import React, { useEffect, useState } from "react";
import { columns } from "./columns";
import { filterConfigs } from "./filtering";
import { studentBulkActions } from "./bulk-actions";

export default function StudentTable() {
  const [data, setData] = useState<Students>([]);
  const [loading, setLoading] = useState(true);
  const [pageCount, setPageCount] = useState(-1);

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  async function fetchStudents(params: {
    pagination: PaginationState;
    sorting: SortingState;
    columnFilters: ColumnFiltersState;
  }): Promise<{ data: Students; pageCount: number; total: number }> {
    const result = await getStudents(params);

    return {
      data: result.data,
      pageCount: result.pageCount,
      total: result.total,
    };
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const result = await fetchStudents({
          pagination,
          sorting,
          columnFilters,
        });
        setData(result.data);
        setPageCount(result.pageCount);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [pagination, sorting, columnFilters]);

  return (
    <>
      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        pageCount={pageCount}
        onPaginationChange={setPagination}
        onSortingChange={setSorting}
        onColumnFiltersChange={setColumnFilters}
        manualPagination={true}
        manualSorting={true}
        manualFiltering={true}
        filterConfigs={filterConfigs}
        searchColumn="name"
        searchPlaceholder="Search students by name..."
        enableBulkActions={true}
        bulkActions={studentBulkActions}
      />
    </>
  );
}
