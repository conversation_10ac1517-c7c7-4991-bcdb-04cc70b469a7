"use client";

import * as React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface DropdownProps {
  trigger: React.ReactNode;
  children: React.ReactNode;
  triggerButton?: boolean;
}

export function CustomDropdown({
  trigger,
  children,
  triggerButton = true,
}: DropdownProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild={triggerButton}>
        {trigger}
      </DropdownMenuTrigger>
      <DropdownMenuContent className="flex flex-col">
        {children}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
