"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";

type Props = {
  row: any;
  columns: any[];
  enableBulkActions: boolean;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onAction?: (action: string, row: any) => void;
  actions: any[];
  renderCell?: (column: any, value: string) => React.ReactNode;
};

type Status = "Active" | "Inactive" | "Suspended";

export function TableRow({
  row,
  columns,
  enableBulkActions,
  isSelected,
  onSelect,
  onAction,
  actions,
  renderCell,
}: Props) {
  const defaultRenderCell = (column: any, value: any) => {
    if (column.key === "status") {
      const getStatusVariant = (status: Status) => {
        switch (status) {
          case "Active":
            return "outline";
          case "Inactive":
            return "secondary";
          case "Suspended":
            return "destructive";
          default:
            return "default";
        }
      };
      return <Badge variant={getStatusVariant(value)}>{value}</Badge>;
    }

    if (column.key === "name") {
      return (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-8 w-8">
            <div className="h-8 w-8 rounded-full bg-accent flex items-center justify-center text-sm font-medium text-accent-foreground">
              {value.charAt(0)}
            </div>
          </div>
          <div className="ml-3">
            <div className="text-sm font-medium">{value}</div>
          </div>
        </div>
      );
    }

    if (column.type === "date") {
      return new Date(value).toLocaleDateString();
    }

    return value;
  };

  return (
    <tr className="hover:bg-gray-50">
      {enableBulkActions && (
        <td className="px-6 py-4">
          <Checkbox
            checked={isSelected}
            onCheckedChange={() => onSelect(row.id)}
          />
        </td>
      )}
      {columns.map((column) => (
        <td
          key={column.key}
          className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
        >
          {renderCell
            ? renderCell(column, row[column.key])
            : defaultRenderCell(column, row[column.key])}
        </td>
      ))}
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="flex items-center justify-end gap-2">
          {actions.map((action) => (
            <Button
              key={action.key}
              onClick={() => onAction?.(action.key, row)}
              variant={
                action.variant === "danger"
                  ? "destructive"
                  : action.variant === "primary"
                    ? "default"
                    : "secondary"
              }
              title={action.label}
            >
              {action.icon}
            </Button>
          ))}
        </div>
      </td>
    </tr>
  );
}
