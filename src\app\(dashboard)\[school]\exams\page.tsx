import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import { withResourceAccess } from "@/components/shared/page-gurad";
import { Actions, Resources } from "@/lib/permissions";
import { Loader2 } from "lucide-react";
import { Suspense } from "react";

// This is a SERVER COMPONENT with server-side protection
async function ExamPage({
  params,
  searchParams,
}: {
  params: Promise<Record<string, string>>;
  searchParams: Promise<Record<string, string>>;
}) {
  const [p, s] = await Promise.all([params, searchParams]);
  // Get authenticated user data on server

  // Server-side data fetching
  // const studentsData = await fetchStudentsData(p.school, s);

  return (
    <div className="space-y-6">
      {/* <PageHeader 
        title="Students" 
        description="Manage student records and information"
      > */}
      {/* Client-side permission check for create button */}
      <ClientRoleGuard resource={Resources.STUDENTS} action={Actions.CREATE}>
        {/* <CreateStudentButton /> */}
        <div>hello</div>
      </ClientRoleGuard>
      {/* </PageHeader> */}

      <Suspense fallback={<Loader2 className="h-8 w-8 animate-spin" />}>
        {/* <StudentsList 
          initialData={studentsData}
          userPermissions={user?.permissions || []}
        /> */}
      </Suspense>
    </div>
  );
}

// Apply the page guard
export default withResourceAccess(ExamPage, {
  resource: Resources.DEPARTMENTS,
  action: Actions.READ,
});

// Server-side data fetching
// async function fetchStudentsData(schoolSlug: string, searchParams: any) {
//   // Your server-side data fetching logic
//   const response = await fetch(
//     `${process.env.BACKEND_URL}/schools/${schoolSlug}/students`,
//     {
//       // Include auth headers from server
//       headers: {
//         Authorization: `Bearer ${process.env.SERVER_API_TOKEN}`,
//       },
//     }
//   );

//   return response.json();
// }
