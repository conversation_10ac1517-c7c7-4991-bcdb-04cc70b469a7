"use client";

import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertTriangleIcon, CheckCircle, Trash2Icon } from "lucide-react";
import { toast } from "sonner";
import z from "zod";
import { useFieldArray, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import {
  FormDatePicker,
  FormInput,
  FormRadioGroup,
  FormSelect,
} from "@/components/forms";
import { useEffect, useState } from "react";
import { getNationality } from "@/data/getNationality";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { Separator } from "@/components/ui/separator";
import {
  child<PERSON>ustodian,
  emergencyContact,
  gradeOptions,
  maritalStatus,
  religiousAffiliation,
} from "@/data/options";
import EnrolmentFormSubitButton from "./submit-button";

const familyBackgroundSchema = z.object({
  // Parent Info Validation
  parent: z.array(
    z.object({
      role: z.string().min(1, { message: "Please select role" }),
      firstName: z
        .string()
        .min(3, { message: "First Name must be at least 3 characters" }),
      middleName: z.string().optional(),
      lastName: z
        .string()
        .min(3, { message: "Last Name must be at least 3 characters" }),
      otherName: z.string().optional(),
      CRA: z.string(),
      email: z.email({ message: "Please enter a valid email address" }),
      mobile: z.number({ error: "Please enter a valid mobile number" }),
      otherMobile: z
        .number({ error: "Please enter a valid mobile number" })
        .optional(),
      occupation: z.string(),
      workplace: z.string().optional(),
      workplaceAddress: z.string().optional(),
      workplaceContact: z.string().optional(),
      workplaceLocation: z.string().optional(),
      religiousAffiliation: z.string().optional(),
    }),
  ),
  siblings: z.array(
    z.object({
      name: z.string().optional(),
      grade: z.string().optional(),
      section: z.string().optional(),
    }),
  ),
  parentMaritalStatus: z.string().min(1, { message: "Please select one" }),
  childCustodian: z.string().min(1, { message: "Please select one" }),
  birthOrder: z.string().optional(),
  totalNOC: z.number().optional(),
  haveSiblings: z.string().optional(),
});

type FamilyBackgroundFormData = z.infer<typeof familyBackgroundSchema>;

interface FamilyBackgroundFormProps {
  initialData?: Partial<FamilyBackgroundFormData>;
}

export default function FamilyBackgroundForm({
  initialData,
}: FamilyBackgroundFormProps) {
  const [nationalityData, setNationalityData] = useState<
    { label: string; value: string }[]
  >([]);

  const form = useForm<FamilyBackgroundFormData>({
    resolver: zodResolver(familyBackgroundSchema),
    defaultValues: {
      parentMaritalStatus: "",
      childCustodian: "",
      birthOrder: "",
      totalNOC: 0,
      haveSiblings: "no",
      parent: [
        {
          role: "",
          firstName: "",
          middleName: "",
          lastName: "",
          otherName: "",
          CRA: "",
          email: "",
          mobile: 0,
          otherMobile: 0,
          occupation: "",
          workplace: "",
          workplaceAddress: "",
          workplaceContact: "",
          workplaceLocation: "PH",
          religiousAffiliation: "",
        },
      ],
      siblings: [
        {
          name: "",
          grade: "",
          section: "",
        },
      ],
      ...initialData,
    },
    mode: "onBlur",
  });

  useEffect(() => {
    if (initialData) {
      form.reset(initialData);
    }

    const fetchNationality = async () => {
      try {
        const data = await getNationality();
        setNationalityData(data);
      } catch (error) {
        console.error("Failed to fetch nationality data:", error);
      }
    };
    fetchNationality();
  }, [initialData, form]);

  const {
    control,
    formState: { errors },
  } = form;

  const {
    fields: parentFields,
    append: appendParent,
    remove: removeParent,
  } = useFieldArray({
    control,
    name: "parent",
  });

  const {
    fields: siblingFields,
    append: appendSibling,
    remove: removeSibling,
  } = useFieldArray({
    control,
    name: "siblings",
  });

  const handleSubmit = async (data: FamilyBackgroundFormData) => {
    try {
      console.log(data);
      // await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error("Form submission failed", {
        description: "Please check your input and try again.",
      });
    }
  };
  return (
    <Card className="w-full">
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Contact Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Emergency Contact</h3>
              <div className="flex flex-col gap-4">
                {parentFields.map((field, index) => (
                  <Accordion
                    type="single"
                    collapsible
                    defaultValue="item-1"
                    key={field.id}
                  >
                    <AccordionItem value="item-1">
                      <AccordionTrigger
                        className={cn(
                          "[&[data-state=closed]>button]:hidden [&[data-state=open]>.alert]:hidden relative !no-underline text-primary text-lg",
                          errors?.parent?.[index] && "text-destructive",
                        )}
                      >
                        {`Contact ${index + 1}`}

                        <div
                          className="absolute right-8 size-9 border border-input shadow-sm hover:bg-accent rounded-md hover:text-accent-foreground flex items-center justify-center"
                          onClick={() => removeParent(index)}
                        >
                          <Trash2Icon className="h-4 w-4 text-destructive" />
                        </div>
                        {errors?.parent?.[index] && (
                          <span className="absolute alert right-8">
                            <AlertTriangleIcon className="h-4 w-4   text-destructive" />
                          </span>
                        )}
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormSelect
                            form={form}
                            name={`parent.${index}.role`}
                            label="Role"
                            placeholder="Select role"
                            options={emergencyContact}
                          />
                          <FormInput
                            form={form}
                            name={`parent.${index}.firstName`}
                            label="First Name"
                            placeholder="Enter first name"
                          />
                          <FormInput
                            form={form}
                            name={`parent.${index}.middleName`}
                            label="Middle Name"
                            placeholder="Enter middle name"
                          />
                          <FormInput
                            form={form}
                            name={`parent.${index}.lastName`}
                            label="Last Name"
                            placeholder="Enter last name"
                          />
                          <FormInput
                            form={form}
                            name={`parent.${index}.otherName`}
                            label="Other Name"
                            placeholder="Enter other name"
                          />
                          <FormInput
                            form={form}
                            name={`parent.${index}.CRA`}
                            label="Current Residential Address"
                            placeholder="Enter your address"
                          />
                          <FormInput
                            form={form}
                            name={`parent.${index}.email`}
                            label="Email"
                            placeholder="Enter your email"
                          />
                          <FormInput
                            form={form}
                            name={`parent.${index}.mobile`}
                            label="Mobile"
                            placeholder="Enter your mobile number"
                          />
                          <FormInput
                            form={form}
                            name={`parent.${index}.otherMobile`}
                            label="Other Mobile"
                            placeholder="Enter your other mobile number"
                          />
                          <FormInput
                            form={form}
                            name={`parent.${index}.occupation`}
                            label="Occupation"
                            placeholder="Enter your occupation"
                          />
                          <FormInput
                            form={form}
                            name={`parent.${index}.workplace`}
                            label="Workplace"
                            placeholder="Enter your workplace"
                          />
                          <FormInput
                            form={form}
                            name={`parent.${index}.workplaceAddress`}
                            label="Workplace Address"
                            placeholder="Enter your workplace address"
                          />
                          <FormInput
                            form={form}
                            name={`parent.${index}.workplaceContact`}
                            label="Workplace Contact"
                            placeholder="Enter your workplace contact"
                          />
                          <FormSelect
                            form={form}
                            name={`parent.${index}.workplaceLocation`}
                            label="Workplace Location"
                            placeholder="Enter your workplace location"
                            options={nationalityData}
                          />
                          <FormSelect
                            form={form}
                            name={`parent.${index}.religiousAffiliation`}
                            label="Religious Affiliation"
                            placeholder="Select religious affiliation"
                            options={religiousAffiliation}
                          />
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                ))}

                <div className="flex justify-end">
                  <Button
                    type="button"
                    className="flex justify-center"
                    size={"sm"}
                    onClick={() =>
                      appendParent({
                        role: "",
                        firstName: "",
                        middleName: "",
                        lastName: "",
                        otherName: "",
                        CRA: "",
                        email: "",
                        mobile: 0,
                        otherMobile: 0,
                        occupation: "",
                        workplace: "",
                        workplaceAddress: "",
                        workplaceContact: "",
                        workplaceLocation: "PH",
                        religiousAffiliation: "",
                      })
                    }
                  >
                    Add More
                  </Button>
                </div>
              </div>
            </div>

            <Separator />

            {/* Siblings Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Siblings Information</h3>
              <div className="flex flex-col gap-4">
                <FormRadioGroup
                  form={form}
                  name="haveSiblings"
                  label="Do you have siblings in the school?"
                  options={[
                    { value: "yes", label: "Yes" },
                    { value: "no", label: "No" },
                  ]}
                  orientation="horizontal"
                />
                {form.watch("haveSiblings") === "yes" && (
                  <>
                    {parentFields.map((field, index) => (
                      <Accordion
                        type="single"
                        collapsible
                        defaultValue="item-1"
                        key={field.id}
                      >
                        <AccordionItem value="item-1">
                          <AccordionTrigger
                            className={cn(
                              "[&[data-state=closed]>button]:hidden [&[data-state=open]>.alert]:hidden relative !no-underline text-primary text-lg",
                              errors?.siblings?.[index] && "text-destructive",
                            )}
                          >
                            {`Sibling ${index + 1}`}

                            <div
                              className="absolute right-8 size-9 border border-input shadow-sm hover:bg-accent rounded-md hover:text-accent-foreground flex items-center justify-center"
                              onClick={() => removeSibling(index)}
                            >
                              <Trash2Icon className="h-4 w-4 text-destructive" />
                            </div>
                            {errors?.siblings?.[index] && (
                              <span className="absolute alert right-8">
                                <AlertTriangleIcon className="h-4 w-4   text-destructive" />
                              </span>
                            )}
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <FormInput
                                form={form}
                                name={`siblings.${index}.name`}
                                label="Full Name"
                                placeholder="Enter name"
                              />
                              <FormSelect
                                form={form}
                                name={`siblings.${index}.grade`}
                                label="Grade Level"
                                placeholder="Enter grade level"
                                options={gradeOptions}
                              />
                              <FormInput
                                form={form}
                                name={`siblings.${index}.section`}
                                label="Section"
                                placeholder="Enter section"
                              />
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    ))}

                    <div className="flex justify-end">
                      <Button
                        type="button"
                        className="flex justify-center"
                        size={"sm"}
                        onClick={() =>
                          appendSibling({
                            name: "",
                            grade: "",
                            section: "",
                          })
                        }
                      >
                        Add More
                      </Button>
                    </div>
                  </>
                )}
              </div>
            </div>

            <Separator />

            {/* Other Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Other Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormSelect
                  form={form}
                  name="parentMaritalStatus"
                  label="Parent's Marital Status"
                  placeholder="Select parent's marital status"
                  options={maritalStatus}
                />
                {form.watch("parentMaritalStatus") === "separated" && (
                  <FormSelect
                    form={form}
                    name="childCustodian"
                    label="With whom does the child stay?"
                    placeholder="Select child custodian"
                    options={childCustodian}
                  />
                )}
                <FormInput
                  form={form}
                  name="birthOrder"
                  label="Birth Order"
                  placeholder="Enter birth order"
                />
                <FormInput
                  form={form}
                  name="totalNOC"
                  type="number"
                  label="Total Number of Children in the Family"
                  placeholder="Enter total number of children"
                />
              </div>
            </div>

            {/* Form Actions */}
            <EnrolmentFormSubitButton
              isSumitting={form.formState.isSubmitting}
            />
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
