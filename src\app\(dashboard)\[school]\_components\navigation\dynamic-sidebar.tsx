"use client";

import type React from "react";

import { useAuth } from "@/contexts/auth-context";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { GraduationCap, ChevronLeft } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { getFilteredNavigation } from "@/data/navigation";
import { roleManager } from "@/data/roles";

interface DynamicSidebarProps {
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

export function DynamicSidebar({
  collapsed = false,
  onToggleCollapse,
}: DynamicSidebarProps) {
  const { user } = useAuth();
  const pathname = usePathname();

  if (!user) return null;

  const userRole = roleManager.getRole(user.role);
  const userPermissions = userRole?.permissions || [];

  const filteredNavItems = getFilteredNavigation(userPermissions);

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  const getRoleColor = (role: string) => {
    const colors = {
      admin: "bg-red-500",
      teacher: "bg-blue-500",
      student: "bg-green-500",
      parent: "bg-purple-500",
    };
    return colors[role as keyof typeof colors] || "bg-gray-500";
  };

  return (
    <div
      className={cn(
        "bg-sidebar border-r border-sidebar-border transition-all duration-300 h-full overflow-auto",
        collapsed ? "w-16" : "w-64"
      )}
    >
      {/* Header */}
      <div className="p-6 border-b border-sidebar-border">
        <div className="flex items-center justify-between">
          <div
            className={cn(
              "flex items-center gap-2",
              collapsed && "justify-center"
            )}
          >
            <GraduationCap className="h-8 w-8 text-sidebar-primary flex-shrink-0" />
            {!collapsed && (
              <div>
                <h2 className="text-lg font-semibold text-sidebar-foreground">
                  SMS
                </h2>
                <p className="text-xs text-sidebar-foreground/60 capitalize">
                  {user.role} Portal
                </p>
              </div>
            )}
          </div>
          {onToggleCollapse && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleCollapse}
              className={cn("h-8 w-8 p-0", collapsed && "hidden")}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <nav className="space-y-1">
          {filteredNavItems.map((item) => {
            const isActive = pathname === item.href;
            const Icon = item.icon;

            return (
              <Link key={item.href} href={item.href}>
                <Button
                  variant={isActive ? "secondary" : "ghost"}
                  className={cn(
                    "w-full h-11 transition-all duration-200",
                    collapsed ? "justify-center px-0" : "justify-start gap-3",
                    isActive
                      ? "bg-sidebar-accent text-sidebar-accent-foreground shadow-sm"
                      : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                  )}
                  title={collapsed ? item.label : undefined}
                >
                  <div className="relative flex-shrink-0">
                    {/* <Icon className="h-4 w-4" />
                    {item.notification && <div className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full" />} */}
                  </div>
                  {!collapsed && (
                    <>
                      <span className="flex-1 text-left">{item.label}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="ml-auto text-xs">
                          {item.badge}
                        </Badge>
                      )}
                    </>
                  )}
                </Button>
              </Link>
            );
          })}
        </nav>
      </ScrollArea>

      {/* User Profile Section */}
      <div className="border-t border-sidebar-border p-4">
        <div
          className={cn(
            "flex items-center gap-3",
            collapsed && "justify-center"
          )}
        >
          <div className="relative">
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={user.avatar || "/placeholder.svg"}
                alt={user.name}
              />
              <AvatarFallback className="text-xs">
                {getInitials(user.name)}
              </AvatarFallback>
            </Avatar>
            <div
              className={cn(
                "absolute -bottom-1 -right-1 h-3 w-3 rounded-full border-2 border-sidebar",
                getRoleColor(user.role)
              )}
            />
          </div>
          {!collapsed && (
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-sidebar-foreground truncate">
                {user.name}
              </p>
              <p className="text-xs text-sidebar-foreground/60 capitalize">
                {user.role}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
