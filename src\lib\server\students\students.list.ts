'use server'

import prisma from "@/lib/prisma";
import { ColumnFiltersState, PaginationState, SortingState } from "@tanstack/react-table";

export async function getStudents({
  pagination: { pageIndex, pageSize },
  sorting,
  columnFilters,
}: {
  pagination: PaginationState;
  sorting: SortingState;
  columnFilters: ColumnFiltersState;
}) {
  const where: any = {};
  const userWhere: any = {};
  const departmentWhere: any = {};

  // 🎛️ Config-driven filter mapping
  const filterConfig: Record<string, (value: any) => any> = {
    name: (v) => ({ contains: v, mode: "insensitive" }),
    email: (v) => ({ contains: v, mode: "insensitive" }),
    status: (v) => ({ in: v }),
    grade: (v) => ({ in: v }),
    year: (v) => ({ in: v.map((x: any) => parseInt(String(x))).filter((n: number) => !isNaN(n)) }),
    enrollmentType: (v) => ({ in: v }),
    department: (v) => ({ code: { in: v } }),
  };

  // Apply filters
  for (const { id, value } of columnFilters) {
    if (!value || (Array.isArray(value) && value.length === 0)) continue;

    if (filterConfig[id]) {
      const result = filterConfig[id](value);
      if (id === "name" || id === "email") userWhere[id] = result;
      else if (id === "department") {
        console.log(result.code)
        departmentWhere.code = result.code.in ? result.code : undefined;
      }
      else where[id] = result;
    } else {
      // default: string contains OR array in
      where[id] = Array.isArray(value)
        ? { in: value }
        : { contains: String(value).trim(), mode: "insensitive" };
    }
  }

  if (Object.keys(userWhere).length) where.user = userWhere;
  if (Object.keys(departmentWhere).length) where.department = departmentWhere;

  // 🎛️ Config-driven sorting
  const sortConfig: Record<string, (dir: "asc" | "desc") => any> = {
    name: (dir) => ({ user: { name: dir } }),
    email: (dir) => ({ user: { email: dir } }),
    department: (dir) => ({ department: { name: dir } }),
  };

  const orderBy =
    sorting.length > 0
      ? sorting.map(({ id, desc }) => (sortConfig[id]?.(desc ? "desc" : "asc") || { [id]: desc ? "desc" : "asc" }))
      : [{ user: { name: "asc" } }];

  // 📊 Query
  const totalCount = await prisma.student.count({ where });
  const students = await prisma.student.findMany({
    where,
    orderBy,
    skip: pageIndex * pageSize,
    take: pageSize,
    include: {
      user: { select: { id: true, name: true, email: true, avatar: true } },
      department: { select: { id: true, name: true, code: true } },
      school: { select: { id: true, name: true, code: true } },
    },
  });

  // 🔄 Transform
  const data = students.map((s) => ({
    id: s.id,
    studentId: s.studentId,
    name: s.user.name,
    email: s.user.email,
    avatar: s.user.avatar,
    status: s.status,
    grade: s.grade || "N/A",
    department: s.department?.code || "N/A",
    departmentName: s.department?.name || "N/A",
    enrollmentType: s.enrollmentType,
    year: s.year || 1,
    gpa: s.gpa ? parseFloat(s.gpa.toString()) : null,
    enrollmentDate: s.enrollmentDate,
    graduationDate: s.graduationDate,
    createdAt: s.createdAt,
    updatedAt: s.updatedAt,
  }));

  return {
    data,
    pageCount: Math.ceil(totalCount / pageSize),
    total: totalCount,
    page: pageIndex + 1,
    pageSize,
  };
}

export type Students = Awaited<ReturnType<typeof getStudents>>['data'];

export async function getStudentsOne({
  pagination: { pageIndex, pageSize },
  sorting,
  columnFilters,
}: {
  pagination: PaginationState;
  sorting: SortingState;
  columnFilters: ColumnFiltersState;
}) {
  const where: any = {};
  const userWhere: any = {};
  const departmentWhere: any = {};

  // 🔎 Filter handling
  columnFilters.forEach(({ id, value }) => {
    if (!value || (Array.isArray(value) && value.length === 0)) return;

    const strVal = typeof value === "string" ? value.trim() : value;

    const stringFilter = () =>
      (typeof strVal === "string" && strVal) ? { contains: strVal, mode: "insensitive" } : undefined;

    const arrayFilter = (transform?: (v: any) => any) => {
      const vals = transform ? (value as any[]).map(transform).filter(v => !isNaN(v)) : value;
      return vals ? { in: vals } : undefined;
    };

    switch (id) {
      case "name": userWhere.name = stringFilter(); break;
      case "email": userWhere.email = stringFilter(); break;
      case "status": where.status = arrayFilter(); break;
      case "grade": where.grade = arrayFilter(); break;
      case "year": where.year = arrayFilter(v => parseInt(String(v))); break;
      case "enrollmentType": where.enrollmentType = arrayFilter(); break;
      case "department": departmentWhere.code = arrayFilter(); break;
      default:
        where[id] = Array.isArray(value) ? arrayFilter() : stringFilter();
    }
  });

  if (Object.keys(userWhere).length) where.user = userWhere;
  if (Object.keys(departmentWhere).length) where.department = departmentWhere;

  // 🔽 Sorting
  const orderBy =
    sorting.length > 0
      ? sorting.map(({ id, desc }) => {
          const dir = desc ? "desc" : "asc";
          const map: Record<string, any> = {
            name: { user: { name: dir } },
            email: { user: { email: dir } },
            department: { department: { name: dir } },
          };
          return map[id] || { [id]: dir };
        })
      : [{ user: { name: "asc" } }];

  // 📊 Count + Fetch
  const totalCount = await prisma.student.count({ where });
  const students = await prisma.student.findMany({
    where,
    orderBy,
    skip: pageIndex * pageSize,
    take: pageSize,
    include: {
      user: { select: { id: true, name: true, email: true, avatar: true } },
      department: { select: { id: true, name: true, code: true } },
      school: { select: { id: true, name: true, code: true } },
    },
  });

  // 🔄 Transform
  const data = students.map((s) => ({
    id: s.id,
    studentId: s.studentId,
    name: s.user.name,
    email: s.user.email,
    avatar: s.user.avatar,
    status: s.status,
    grade: s.grade || "N/A",
    department: s.department?.code || "N/A",
    departmentName: s.department?.name || "N/A",
    enrollmentType: s.enrollmentType,
    year: s.year || 1,
    gpa: s.gpa ? parseFloat(s.gpa.toString()) : null,
    enrollmentDate: s.enrollmentDate,
    graduationDate: s.graduationDate,
    createdAt: s.createdAt,
    updatedAt: s.updatedAt,
  }));

  return {
    data,
    pageCount: Math.ceil(totalCount / pageSize),
    total: totalCount,
    page: pageIndex + 1,
    pageSize,
  };
}




type FilterHandler = (value: any) => any;
type SortHandler = (dir: "asc" | "desc") => any;

interface QueryOptions<T> {
  model: any; // e.g. prisma.student
  pagination: PaginationState;
  sorting: SortingState;
  filters: ColumnFiltersState;
  filterConfig?: Record<string, FilterHandler>;
  sortConfig?: Record<string, SortHandler>;
  include?: any; // prisma include
  transform?: (row: any) => T; // data transformer
  sortingFallback?: string; // fallback sort
}

export async function queryTable<T>({
  model,
  pagination: { pageIndex, pageSize },
  sorting,
  filters,
  filterConfig = {},
  sortConfig = {},
  include,
  transform,
}: QueryOptions<T>) {
  const where: any = {};

  // ✅ Filtering
  for (const { id, value } of filters) {
    if (!value || (Array.isArray(value) && value.length === 0)) continue;

    if (filterConfig[id]) {
      where[id] = filterConfig[id](value);
    } else {
      // default: array -> IN, string -> contains
      where[id] = Array.isArray(value)
        ? { in: value }
        : { contains: String(value).trim(), mode: "insensitive" };
    }
  }

  // ✅ Sorting
  const orderBy =
    sorting.length > 0
      ? sorting.map(({ id, desc }) =>
          sortConfig[id]?.(desc ? "desc" : "asc") || { [id]: desc ? "desc" : "asc" }
        )
      : [{ id: "asc" }]; // fallback

  // ✅ Query count + data
  const totalCount = await model.count({ where });
  const rows = await model.findMany({
    where,
    orderBy,
    skip: pageIndex * pageSize,
    take: pageSize,
    include,
  });

  // ✅ Transform data if needed
  const data = transform ? rows.map(transform) : rows;

  return {
    data,
    pageCount: Math.ceil(totalCount / pageSize),
    total: totalCount,
    page: pageIndex + 1,
    pageSize,
  };
}

export async function getStudentsT(opts: {
  pagination: PaginationState;
  sorting: SortingState;
  columnFilters: ColumnFiltersState;
}) {
  return queryTable({
    model: prisma.student,
    pagination: opts.pagination,
    sorting: opts.sorting,
    filters: opts.columnFilters,
    include: {
      user: { select: { id: true, name: true, email: true, avatar: true } },
      department: { select: { id: true, name: true, code: true } },
      school: { select: { id: true, name: true, code: true } },
    },
    filterConfig: {
      year: (v) => ({
        in: v.map((x: any) => parseInt(String(x))).filter((n: number) => !isNaN(n)),
      }),
    },
    sortConfig: {
      name: (dir) => ({ user: { name: dir } }),
      email: (dir) => ({ user: { email: dir } }),
      department: (dir) => ({ department: { name: dir } }),
    },
    transform: (s) => ({
      id: s.id,
      studentId: s.studentId,
      name: s.user.name,
      email: s.user.email,
      avatar: s.user.avatar,
      status: s.status,
      grade: s.grade || "N/A",
      department: s.department?.code || "N/A",
      departmentName: s.department?.name || "N/A",
      enrollmentType: s.enrollmentType,
      year: s.year || 1,
      gpa: s.gpa ? parseFloat(s.gpa.toString()) : null,
      enrollmentDate: s.enrollmentDate,
      graduationDate: s.graduationDate,
      createdAt: s.createdAt,
      updatedAt: s.updatedAt,
    }),
  });
}