"use client";

import { DataTable } from "@/components/shared/data-table/data-table";
import React, { useEffect, useState } from "react";
import { columns } from "./columns";
import { Department } from "@/lib/types";

export default function DepartmentTable() {
  const [data, setData] = useState<Department>([]);
  const [loading, setLoading] = useState(true);

  async function fetchDepartments(): Promise<{ data: Department; total: number }> {
    const result = await getDepartments();

    return {
      data: result.data,
      total: result.total,
    };
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const result = await fetchDepartments();
        setData(result.data);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return (
    <>
      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        searchColumn="name"
        searchPlaceholder="Search department by name..."
      />
    </>
  );
}
