// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["kingdom_sis", "public"]
}

// Enums
enum UserRole {
  admin
  teacher
  student
  parent

  @@schema("kingdom_sis")
}

enum StudentStatus {
  active
  inactive
  suspended
  graduated

  @@schema("kingdom_sis")
}

enum EnrollmentType {
  fulltime
  parttime
  online
  hybrid

  @@schema("kingdom_sis")
}

// Models
model User {
  id            String   @id @default(uuid()) @db.Uuid
  email         String   @unique
  passwordHash  String?  @map("password_hash")
  name          String
  role          UserRole @default(student)
  avatar        String?
  isActive      Boolean  @default(true) @map("is_active")
  emailVerified Boolean  @default(false) @map("email_verified")
  createdAt     DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt     DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  student Student?

  @@map("users")
  @@schema("kingdom_sis")
}

model School {
  id        String   @id @default(uuid()) @db.Uuid
  name      String
  code      String   @unique
  address   String?
  phone     String?
  email     String?
  website   String?
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  departments Department[]
  students    Student[]

  @@map("schools")
  @@schema("kingdom_sis")
}

model Department {
  id          String   @id @default(uuid()) @db.Uuid
  schoolId    String   @map("school_id") @db.Uuid
  name        String
  code        String
  description String?
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt   DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  school   School    @relation(fields: [schoolId], references: [id], onDelete: Cascade)
  students Student[]

  @@unique([schoolId, code])
  @@map("departments")
  @@schema("kingdom_sis")
}

model Student {
  id             String         @id @default(uuid()) @db.Uuid
  userId         String         @unique @map("user_id") @db.Uuid
  schoolId       String         @map("school_id") @db.Uuid
  studentId      String         @unique @map("student_id")
  status         StudentStatus  @default(active)
  enrollmentType EnrollmentType @default(fulltime) @map("enrollment_type")
  grade          String?
  year           Int?
  departmentId   String?        @map("department_id") @db.Uuid
  enrollmentDate DateTime       @default(now()) @map("enrollment_date") @db.Date
  graduationDate DateTime?      @map("graduation_date") @db.Date
  gpa            Decimal?       @db.Decimal(3, 2)
  createdAt      DateTime       @default(now()) @map("created_at") @db.Timestamptz
  updatedAt      DateTime       @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  user       User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  school     School      @relation(fields: [schoolId], references: [id], onDelete: Cascade)
  department Department? @relation(fields: [departmentId], references: [id])

  @@map("students")
  @@schema("kingdom_sis")
}
