"use client";

import { Card, CardContent } from "@/components/ui/card";
import EnrolmentFormSubitButton from "./submit-button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { useEffect } from "react";
import z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { Motivation, Sources } from "@/data/options";

const otherInformationSchema = z.object({
  sources: z
    .array(z.string())
    .refine((value) => value.some((source) => source), {
      message: "You have to select at least one item.",
    }),
  motivations: z
    .array(z.string())
    .refine((value) => value.some((motivation) => motivation), {
      message: "You have to select at least one item.",
    }),
  otherSourcesText: z.string().optional(),
  otherMotivationText: z.string().optional(),
});

type OtherInformationFormData = z.infer<typeof otherInformationSchema>;

interface OtherInformationFormProps {
  initialData?: Partial<OtherInformationFormData>;
}

export default function OtherInformationForm({
  initialData,
}: OtherInformationFormProps) {
  const form = useForm<OtherInformationFormData>({
    resolver: zodResolver(otherInformationSchema),
    defaultValues: {
      sources: [""],
      motivations: [""],
    },
  });

  useEffect(() => {
    if (initialData) {
      form.reset(initialData);
    }
  }, [initialData, form]);

  const handleSubmit = async (data: OtherInformationFormData) => {
    try {
      console.log(data);
      // await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error("Form submission failed", {
        description: "Please check your input and try again.",
      });
    }
  };
  return (
    <Card className="w-full">
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Other Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Other Information</h3>
              <div className="flex flex-col gap-4 w-full">
                <div className="flex flex-col gap-2">
                  <FormField
                    control={form.control}
                    name="sources"
                    render={() => (
                      <FormItem>
                        <div className="mb-4">
                          <FormLabel className="text-primary text-lg">
                            HOW DID YOU LEARN ABOUT OUR SCHOOL?
                          </FormLabel>
                          <FormDescription>
                            Select the related items.
                          </FormDescription>
                        </div>
                        {Sources.map((source: any) => (
                          <FormField
                            key={source.id}
                            control={form.control}
                            name="sources"
                            render={({ field }) => {
                              return (
                                <FormItem
                                  key={source.id}
                                  className="flex flex-row items-start space-x-3 space-y-0"
                                >
                                  <FormControl>
                                    <Checkbox
                                      checked={field.value?.includes(source.id)}
                                      onCheckedChange={(checked) => {
                                        return checked
                                          ? field.onChange([
                                              ...field.value,
                                              source.id,
                                            ])
                                          : field.onChange(
                                              field.value?.filter(
                                                (value: string) =>
                                                  value !== source.id,
                                              ),
                                            );
                                      }}
                                    />
                                  </FormControl>
                                  <FormLabel className="text-sm font-normal">
                                    {source.label}
                                  </FormLabel>
                                </FormItem>
                              );
                            }}
                          />
                        ))}
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {form.watch("sources").includes("sourcesOther") && (
                  <FormField
                    control={form.control}
                    name="otherSourcesText"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Specify your sources</FormLabel>
                        <FormControl className="h-28">
                          <Textarea
                            placeholder="0/50 characters"
                            {...field}
                            className="textarea rounded-2xl"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>

              <div className="flex flex-col gap-4 w-full">
                <div className="flex flex-col gap-2">
                  <FormField
                    control={form.control}
                    name="motivations"
                    render={() => (
                      <FormItem>
                        <div className="mb-4">
                          <FormLabel className="text-primary text-lg">
                            {" "}
                            WHY DO YOU WANT TO STUDY AT OUR SCHOOL?
                          </FormLabel>
                          <FormDescription>
                            Select the related items.
                          </FormDescription>
                        </div>
                        {Motivation.map((motivation: any) => (
                          <FormField
                            key={motivation.id}
                            control={form.control}
                            name="motivations"
                            render={({ field }) => {
                              return (
                                <FormItem
                                  key={motivation.id}
                                  className="flex flex-row items-start space-x-3 space-y-0"
                                >
                                  <FormControl>
                                    <Checkbox
                                      checked={field.value?.includes(
                                        motivation.id,
                                      )}
                                      onCheckedChange={(checked) => {
                                        return checked
                                          ? field.onChange([
                                              ...field.value,
                                              motivation.id,
                                            ])
                                          : field.onChange(
                                              field.value?.filter(
                                                (value: string) =>
                                                  value !== motivation.id,
                                              ),
                                            );
                                      }}
                                    />
                                  </FormControl>
                                  <FormLabel className="text-sm font-normal">
                                    {motivation.label}
                                  </FormLabel>
                                </FormItem>
                              );
                            }}
                          />
                        ))}
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {form.watch("motivations").includes("motivationOther") && (
                  <FormField
                    control={form.control}
                    name="otherMotivationText"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Specify your motivation</FormLabel>
                        <FormControl className="h-28">
                          <Textarea
                            placeholder="0/50 characters"
                            {...field}
                            className="textarea rounded-2xl"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
            </div>

            {/* Form Actions */}
            <EnrolmentFormSubitButton
              isSumitting={form.formState.isSubmitting}
              isLast
            />
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
