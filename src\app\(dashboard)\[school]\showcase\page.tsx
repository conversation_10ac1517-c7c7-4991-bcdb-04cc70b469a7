"use client"

import { FormShowcase } from "@/components/form-showcase"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function ShowcasePage() {
  return (
    <div className="container mx-auto py-8 px-4 space-y-8">
      <div className="flex items-center gap-4">
        <Link href="/">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Students
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Form Components Showcase</h1>
          <p className="text-muted-foreground">Interactive demonstration of all available form components</p>
        </div>
      </div>

      <FormShowcase />
    </div>
  )
}
