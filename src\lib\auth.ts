import type { User, UserRole } from "./types"
import { BASE_PERMISSIONS, getUserByEmail } from "./mock-data"
import { roleManager } from "@/data/roles"
import { NAVIGATION_CONFIG, NavigationItem } from "@/data/navigation"

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
}

export interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
  hasPermission: (resource: string, action: string) => boolean
}

// Mock authentication function
export const authenticateUser = async (email: string, password: string): Promise<User | null> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000))

  // Simple mock authentication - in real app, this would validate against backend
  const user = getUserByEmail(email)
  if (user && password === "password123") {
    return user
  }
  return null
}

// Permission checking utility
export const checkPermission = (userRole: UserRole, resource: string, action: string): boolean => {
  const role = roleManager.getRole(userRole)
  const permissions = BASE_PERMISSIONS.filter((p) => role?.permissions.includes(p.id)) || []
  return permissions.some((permission) => permission.resource === resource && permission.action === action)
}

// Route protection utility
export const getAuthorizedRoutes = (role: UserRole): string[] => {
  const authorizedRoutes: string[] = []
  
  const checkNavigationAccess = (items: NavigationItem[]): void => {
    items.forEach(item => {
      // Check if user has any of the required permissions for this item
      const hasAccess = item.permissions.length === 0 || 
        item.permissions.some(permission => {
          const [resource, action] = permission.split('.')
          return checkPermission(role, resource, action)
        })
      
      if (hasAccess) {
        authorizedRoutes.push(item.href)
        
        // Recursively check children
        if (item.children) {
          checkNavigationAccess(item.children)
        }
      }
    })
  }
  
  checkNavigationAccess(NAVIGATION_CONFIG)
  return [...new Set(authorizedRoutes)] // Remove duplicates
}
