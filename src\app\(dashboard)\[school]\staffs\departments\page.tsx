import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import { But<PERSON> } from "@/components/ui/button";
import { Actions, Resources } from "@/lib/permissions";
import { Plus } from "lucide-react";
import PageWrapper from "../../_components/layouts/PageWrapper";
import StaffDepartmentTable from "./table";
import { CustomSheet } from "@/components/shared/CustomSheet";
import { withResourceAccess } from "@/components/shared/page-gurad";

const breadcrumbItems = [
  { label: "Home", href: "/" },
  { label: "Staffs", href: "/staffs" },
  { label: "Departments" },
];

async function StaffDepartments({
  params,
}: {
  params: Promise<Record<string, string>>;
}) {
  const school = await params;
  const renderButton = () => {
    return (
      <ClientRoleGuard
        resource={Resources.STAFF_DEPARTMENTS}
        action={Actions.CREATE}
      >
        <CustomSheet
          title="Add Department"
          trigger={
            <Button>
              <Plus /> Add Department
            </Button>
          }
        >
          <div>Add Department Form</div>
        </CustomSheet>
      </ClientRoleGuard>
    );
  };

  return (
    <PageWrapper
      pgTitle="Manage Departments"
      pgDescription="Manage department records and information"
      breadcrumbItems={breadcrumbItems}
      headerButton={renderButton()}
      school={school.school}
    >
      <StaffDepartmentTable />
    </PageWrapper>
  );
}

export default withResourceAccess(StaffDepartments, {
  resource: Resources.STAFF_DEPARTMENTS,
});
