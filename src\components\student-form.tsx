"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  FormInput,
  FormTextarea,
  FormSelect,
  FormCheckbox,
  FormRadioGroup,
  FormCombobox,
  FormDatePicker,
  FormMultiSelect,
} from "@/components/forms";
import {
  studentSchema,
  type StudentFormData,
  gradeOptions,
  subjectOptions,
  scholarshipOptions,
  genderOptions,
} from "@/lib/validations/student";

interface StudentFormProps {
  initialData?: Partial<StudentFormData>;
  onSubmit: (data: StudentFormData) => Promise<void>;
  isLoading?: boolean;
  mode?: "create" | "edit";
}

export function StudentForm({
  initialData,
  onSubmit,
  isLoading = false,
  mode = "create",
}: StudentFormProps) {
  const form = useForm<StudentFormData>({
    resolver: zodResolver(studentSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      gender: undefined,
      grade: "",
      subjects: [],
      address: "",
      emergencyContact: "",
      hasScholarship: false,
      scholarshipType: "",
      notes: "",
      ...initialData,
    },
  });

  const watchHasScholarship = form.watch("hasScholarship");

  // Reset form when initialData changes (for edit mode)
  useEffect(() => {
    if (initialData) {
      form.reset(initialData);
    }
  }, [initialData, form]);

  const handleSubmit = async (data: StudentFormData) => {
    try {
      await onSubmit(data);
      if (mode === "create") {
        form.reset();
      }
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error("Form submission failed", {
        description: "Please check your input and try again.",
      });
    }
  };

  const handleReset = () => {
    form.reset();
    toast.info("Form reset", {
      description: "All fields have been cleared.",
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>
          {mode === "create" ? "Add New Student" : "Edit Student"}
        </CardTitle>
        <CardDescription>
          {mode === "create"
            ? "Fill in the student information to add them to the system."
            : "Update the student information below."}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Personal Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Personal Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  form={form}
                  name="firstName"
                  label="First Name"
                  placeholder="Enter first name"
                />
                <FormInput
                  form={form}
                  name="lastName"
                  label="Last Name"
                  placeholder="Enter last name"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  form={form}
                  name="email"
                  label="Email Address"
                  type="email"
                  placeholder="<EMAIL>"
                />
                <FormInput
                  form={form}
                  name="phone"
                  label="Phone Number"
                  type="tel"
                  placeholder="(*************"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormDatePicker
                  form={form}
                  name="dateOfBirth"
                  label="Date of Birth"
                  placeholder="Select date of birth"
                />
                <FormRadioGroup
                  form={form}
                  name="gender"
                  label="Gender"
                  options={genderOptions}
                  orientation="horizontal"
                />
              </div>
            </div>

            <Separator />

            {/* Academic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Academic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormCombobox
                  form={form}
                  name="grade"
                  label="Grade Level"
                  placeholder="Select grade"
                  options={gradeOptions}
                  searchPlaceholder="Search grades..."
                />
                <FormMultiSelect
                  form={form}
                  name="subjects"
                  label="Subjects"
                  placeholder="Select subjects"
                  options={subjectOptions}
                  searchPlaceholder="Search subjects..."
                  maxSelected={6}
                />
              </div>
            </div>

            <Separator />

            {/* Contact Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Contact Information</h3>
              <FormTextarea
                form={form}
                name="address"
                label="Home Address"
                placeholder="Enter full address"
                rows={3}
              />
              <FormInput
                form={form}
                name="emergencyContact"
                label="Emergency Contact"
                type="tel"
                placeholder="Emergency contact number"
                description="Phone number of parent or guardian"
              />
            </div>

            <Separator />

            {/* Scholarship Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Scholarship Information</h3>
              <FormCheckbox
                form={form}
                name="hasScholarship"
                label="Student has a scholarship"
                description="Check if the student is receiving any form of scholarship"
              />

              {watchHasScholarship && (
                <FormSelect
                  form={form}
                  name="scholarshipType"
                  label="Scholarship Type"
                  placeholder="Select scholarship type"
                  options={scholarshipOptions}
                />
              )}
            </div>

            <Separator />

            {/* Additional Notes */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Additional Information</h3>
              <FormTextarea
                form={form}
                name="notes"
                label="Notes"
                placeholder="Any additional notes about the student..."
                rows={4}
                description="Optional notes about the student's special needs, achievements, etc."
              />
            </div>

            {/* Form Actions */}
            <div className="flex gap-4 pt-4">
              <Button
                type="submit"
                disabled={isLoading}
                className="flex-1 md:flex-none"
              >
                {isLoading
                  ? mode === "create"
                    ? "Adding Student..."
                    : "Updating Student..."
                  : mode === "create"
                    ? "Add Student"
                    : "Update Student"}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={handleReset}
                disabled={isLoading}
              >
                Reset Form
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
