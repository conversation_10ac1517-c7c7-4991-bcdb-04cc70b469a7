"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { ChevronDown, ChevronUp } from "lucide-react";

export function TableHeader({
  columns,
  enableBulkActions,
  selectedCount,
  totalCount,
  onSelectAll,
  sortBy,
  sortOrder,
  onSort,
}: {
  columns: { key: string; label: string; sortable?: boolean }[];
  enableBulkActions: boolean;
  selectedCount: number;
  totalCount: number;
  onSelectAll: () => void;
  sortBy: string;
  sortOrder: "asc" | "desc";
  onSort: (key: string) => void;
}) {
  return (
    <thead className="bg-gray-50">
      <tr>
        {enableBulkActions && (
          <th className="w-4 px-6 py-3">
            <Checkbox
              checked={selectedCount === totalCount && totalCount > 0}
              onCheckedChange={onSelectAll}
            />
          </th>
        )}
        {columns.map((column) => (
          <th
            key={column.key}
            className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
              column.sortable ? "cursor-pointer hover:bg-accent" : ""
            }`}
            onClick={column.sortable ? () => onSort(column.key) : undefined}
          >
            <div className="flex items-center">
              {column.label}
              {column.sortable &&
                sortBy === column.key &&
                (sortOrder === "asc" ? (
                  <ChevronUp className="w-4 h-4 ml-1" />
                ) : (
                  <ChevronDown className="w-4 h-4 ml-1" />
                ))}
            </div>
          </th>
        ))}
        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
          Actions
        </th>
      </tr>
    </thead>
  );
}
