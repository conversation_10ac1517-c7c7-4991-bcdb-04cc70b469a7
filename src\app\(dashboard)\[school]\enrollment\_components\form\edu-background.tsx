"use client";

import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertTriangleIcon, CheckCircle, Trash2Icon } from "lucide-react";
import { toast } from "sonner";
import z from "zod";
import { useFieldArray, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { FormInput, FormDatePicker, FormSelect } from "@/components/forms";
import { educationLevel, gradeOptions } from "@/data/options";
import EnrolmentFormSubitButton from "./submit-button";

const educationalBackgroundSchema = z.object({
  education: z.array(
    z.object({
      educationLevel: z.string().min(1, "Please select an education level"),
      schoolName: z
        .string()
        .min(2, "School name must be at least 2 characters"),
      schoolAddress: z
        .string()
        .min(2, "School address must be at least 2 characters"),
      gradeFrom: z.string().min(1, "Please select a grade"),
      gradeTo: z.string().min(1, "Please select a grade"),
      startYear: z.date().min(1, "Please select a year"),
      endYear: z.date().min(1, "Please select a year"),
    }),
  ),
});

export type EducationalBackgroundFormData = z.infer<
  typeof educationalBackgroundSchema
>;

interface EducationalBackgroundFormProps {
  initialData?: Partial<EducationalBackgroundFormData>;
}

export function EducationalBackgroundForm({
  initialData,
}: EducationalBackgroundFormProps) {
  const form = useForm<EducationalBackgroundFormData>({
    resolver: zodResolver(educationalBackgroundSchema),
    defaultValues: {
      education: [
        {
          educationLevel: "",
          schoolName: "",
          schoolAddress: "",
          gradeFrom: "",
          gradeTo: "",
          startYear: new Date(),
          endYear: new Date(),
        },
      ],
    },
    mode: "onBlur",
  });

  const {
    control,
    formState: { errors },
  } = form;

  const { fields, append, remove } = useFieldArray({
    control,
    name: "education",
  });

  const handleSubmit = async (data: EducationalBackgroundFormData) => {
    try {
      console.log(data);
      // await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error("Form submission failed", {
        description: "Please check your input and try again.",
      });
    }
  };

  return (
    <Card className="w-full">
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Academic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Educational Background</h3>
              <div className="flex flex-col gap-4">
                {fields.map((field, index) => (
                  <Accordion
                    type="single"
                    collapsible
                    defaultValue="item-1"
                    key={field.id}
                  >
                    <AccordionItem value="item-1">
                      <AccordionTrigger
                        className={cn(
                          "[&[data-state=closed]>button]:hidden [&[data-state=open]>.alert]:hidden relative !no-underline text-primary text-lg",
                          errors?.education?.[index] && "text-destructive",
                        )}
                      >
                        {`Education ${index + 1}`}

                        <div
                          className="absolute right-8 size-9 border border-input shadow-sm hover:bg-accent rounded-md hover:text-accent-foreground flex items-center justify-center"
                          onClick={() => remove(index)}
                        >
                          <Trash2Icon className="h-4 w-4 text-destructive" />
                        </div>
                        {errors?.education?.[index] && (
                          <span className="absolute alert right-8">
                            <AlertTriangleIcon className="h-4 w-4   text-destructive" />
                          </span>
                        )}
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormSelect
                            form={form}
                            name={`education.${index}.educationLevel`}
                            label="Education Level"
                            placeholder="Select education level"
                            options={educationLevel}
                          />
                          <FormInput
                            form={form}
                            name={`education.${index}.schoolName`}
                            label="School Name"
                            placeholder="Enter school name"
                          />
                          <FormInput
                            form={form}
                            name={`education.${index}.schoolAddress`}
                            label="School Address"
                            placeholder="Enter school address"
                          />
                          <FormSelect
                            form={form}
                            name={`education.${index}.gradeFrom`}
                            label="Grade from"
                            placeholder="Select grade"
                            options={gradeOptions}
                          />
                          <FormSelect
                            form={form}
                            name={`education.${index}.gradeTo`}
                            label="Grade to"
                            placeholder="Select grade"
                            options={gradeOptions}
                          />
                          <FormDatePicker
                            form={form}
                            name={`education.${index}.startYear`}
                            label="Start Year"
                            placeholder="Select date"
                          />
                          <FormDatePicker
                            form={form}
                            name={`education.${index}.endYear`}
                            label="End Year"
                            placeholder="Select date"
                          />
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                ))}

                <div className="flex justify-end">
                  <Button
                    type="button"
                    className="flex justify-center"
                    size={"sm"}
                    onClick={() =>
                      append({
                        educationLevel: "",
                        schoolName: "",
                        schoolAddress: "",
                        gradeFrom: "",
                        gradeTo: "",
                        startYear: new Date(),
                        endYear: new Date(),
                      })
                    }
                  >
                    Add More
                  </Button>
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <EnrolmentFormSubitButton
              isSumitting={form.formState.isSubmitting}
            />
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
