"use client";

import { ReactNode } from "react";
import { usePermissions } from "@/hooks/use-permissions";
import { Actions } from "@/lib/permissions";

interface RoleGuardProps {
  children: ReactNode;
  resource: string;
  action?: string;
  context?: any;
  fallback?: ReactNode;
  requireAll?: boolean; // If multiple permissions, require all vs any
}

export function RoleGuard({
  children,
  resource,
  action = Actions.READ,
  context,
  fallback = null,
  requireAll = true,
}: RoleGuardProps) {
  const { hasPermission } = usePermissions();

  // Handle multiple resources/actions
  if (Array.isArray(resource)) {
    const permissions = resource.map((res, index) =>
      hasPermission(
        res,
        Array.isArray(action) ? action[index] : action,
        context
      )
    );

    const hasAccess = requireAll
      ? permissions.every(Boolean)
      : permissions.some(Boolean);

    return hasAccess ? <>{children}</> : <>{fallback}</>;
  }

  // Single resource check
  const hasAccess = hasPermission(resource, action, context);

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}

// Usage examples:
// <RoleGuard resource="students" action="create">
//   <CreateStudentButton />
// </RoleGuard>

// <RoleGuard resource="grades" action="update" context={{ classId: "123" }}>
//   <EditGradesForm />
// </RoleGuard>
