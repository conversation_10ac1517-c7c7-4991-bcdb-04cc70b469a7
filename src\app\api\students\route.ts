import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { ColumnFiltersState, PaginationState, SortingState } from '@tanstack/react-table';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { pagination, sorting, columnFilters } = body as {
      pagination: PaginationState;
      sorting: SortingState;
      columnFilters: ColumnFiltersState;
    };

    console.log('📥 Received request:', { pagination, sorting, columnFilters });

    // Build where clause for filtering
    const where: any = {};
    const userWhere: any = {};
    const departmentWhere: any = {};

    columnFilters.forEach((filter) => {
      if (!filter.value) return;

      console.log(`🔧 Processing filter:`, filter);

      switch (filter.id) {
        case 'name':
          if (typeof filter.value === 'string' && filter.value.trim()) {
            userWhere.name = {
              contains: filter.value.trim(),
              mode: 'insensitive',
            };
          }
          break;

        case 'email':
          if (typeof filter.value === 'string' && filter.value.trim()) {
            userWhere.email = {
              contains: filter.value.trim(),
              mode: 'insensitive',
            };
          }
          break;

        case 'status':
          if (Array.isArray(filter.value) && filter.value.length > 0) {
            where.status = {
              in: filter.value,
            };
          }
          break;

        case 'grade':
          if (Array.isArray(filter.value) && filter.value.length > 0) {
            where.grade = {
              in: filter.value,
            };
          }
          break;

        case 'year':
          if (Array.isArray(filter.value) && filter.value.length > 0) {
            // Convert string values to numbers
            const yearValues = filter.value.map(v => parseInt(String(v))).filter(v => !isNaN(v));
            if (yearValues.length > 0) {
              where.year = {
                in: yearValues,
              };
            }
          }
          break;

        case 'enrollmentType':
          if (Array.isArray(filter.value) && filter.value.length > 0) {
            where.enrollmentType = {
              in: filter.value,
            };
          }
          break;

        case 'department':
          if (Array.isArray(filter.value) && filter.value.length > 0) {
            departmentWhere.code = {
              in: filter.value,
            };
          }
          break;

        default:
          // Handle other potential filters
          if (Array.isArray(filter.value) && filter.value.length > 0) {
            where[filter.id] = {
              in: filter.value,
            };
          } else if (typeof filter.value === 'string' && filter.value.trim()) {
            where[filter.id] = {
              contains: filter.value.trim(),
              mode: 'insensitive',
            };
          }
          break;
      }
    });

    // Add user and department filters to main where clause
    if (Object.keys(userWhere).length > 0) {
      where.user = userWhere;
    }
    if (Object.keys(departmentWhere).length > 0) {
      where.department = departmentWhere;
    }

    console.log('🔍 Final where clause:', JSON.stringify(where, null, 2));

    // Build orderBy clause for sorting
    const orderBy: any[] = [];
    if (sorting.length > 0) {
      sorting.forEach((sort) => {
        switch (sort.id) {
          case 'name':
            orderBy.push({
              user: {
                name: sort.desc ? 'desc' : 'asc',
              },
            });
            break;
          case 'email':
            orderBy.push({
              user: {
                email: sort.desc ? 'desc' : 'asc',
              },
            });
            break;
          case 'department':
            orderBy.push({
              department: {
                name: sort.desc ? 'desc' : 'asc',
              },
            });
            break;
          default:
            orderBy.push({
              [sort.id]: sort.desc ? 'desc' : 'asc',
            });
            break;
        }
      });
    } else {
      // Default sorting by name
      orderBy.push({
        user: {
          name: 'asc',
        },
      });
    }

    console.log('📊 Order by:', JSON.stringify(orderBy, null, 2));

    // Get total count for pagination
    const totalCount = await prisma.student.count({
      where,
    });

    console.log(`📈 Total matching records: ${totalCount}`);

    // Get paginated results
    const { pageIndex, pageSize } = pagination;
    const skip = pageIndex * pageSize;

    const students = await prisma.student.findMany({
      where,
      orderBy,
      skip,
      take: pageSize,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        department: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
        school: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    console.log(`📋 Retrieved ${students.length} students for page ${pageIndex + 1}`);

    // Transform data to match the expected format
    const transformedStudents = students.map((student) => ({
      id: student.id,
      studentId: student.studentId,
      name: student.user.name,
      email: student.user.email,
      avatar: student.user.avatar,
      status: student.status,
      grade: student.grade || 'N/A',
      department: student.department?.code || 'N/A',
      departmentName: student.department?.name || 'N/A',
      enrollmentType: student.enrollmentType,
      year: student.year || 1,
      gpa: student.gpa ? parseFloat(student.gpa.toString()) : null,
      enrollmentDate: student.enrollmentDate,
      graduationDate: student.graduationDate,
      createdAt: student.createdAt,
      updatedAt: student.updatedAt,
    }));

    const pageCount = Math.ceil(totalCount / pageSize);

    console.log(`✅ Returning ${transformedStudents.length} students, page ${pageIndex + 1} of ${pageCount}`);

    return NextResponse.json({
      data: transformedStudents,
      pageCount,
      total: totalCount,
      page: pageIndex + 1,
      pageSize,
    });

  } catch (error) {
    console.error('❌ Error fetching students:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch students',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ 
    message: 'Use POST method to fetch students with filters',
    endpoints: {
      POST: '/api/students - Fetch students with filtering, sorting, and pagination'
    }
  });
}
