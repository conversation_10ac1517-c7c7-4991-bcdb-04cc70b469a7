-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "kingdom_sis";

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "kingdom_sis"."UserRole" AS ENUM ('admin', 'teacher', 'student', 'parent');

-- CreateEnum
CREATE TYPE "kingdom_sis"."StudentStatus" AS ENUM ('active', 'inactive', 'suspended', 'graduated');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "kingdom_sis"."EnrollmentType" AS ENUM ('fulltime', 'parttime', 'online', 'hybrid');

-- CreateTable
CREATE TABLE "kingdom_sis"."users" (
    "id" UUID NOT NULL,
    "email" TEXT NOT NULL,
    "password_hash" TEXT,
    "name" TEXT NOT NULL,
    "role" "kingdom_sis"."UserRole" NOT NULL DEFAULT 'student',
    "avatar" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "email_verified" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kingdom_sis"."schools" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "address" TEXT,
    "phone" TEXT,
    "email" TEXT,
    "website" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "schools_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kingdom_sis"."departments" (
    "id" UUID NOT NULL,
    "school_id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "departments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kingdom_sis"."students" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "school_id" UUID NOT NULL,
    "student_id" TEXT NOT NULL,
    "status" "kingdom_sis"."StudentStatus" NOT NULL DEFAULT 'active',
    "enrollment_type" "kingdom_sis"."EnrollmentType" NOT NULL DEFAULT 'fulltime',
    "grade" TEXT,
    "year" INTEGER,
    "department_id" UUID,
    "enrollment_date" DATE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "graduation_date" DATE,
    "gpa" DECIMAL(3,2),
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "students_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "kingdom_sis"."users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "schools_code_key" ON "kingdom_sis"."schools"("code");

-- CreateIndex
CREATE UNIQUE INDEX "departments_school_id_code_key" ON "kingdom_sis"."departments"("school_id", "code");

-- CreateIndex
CREATE UNIQUE INDEX "students_user_id_key" ON "kingdom_sis"."students"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "students_student_id_key" ON "kingdom_sis"."students"("student_id");

-- AddForeignKey
ALTER TABLE "kingdom_sis"."departments" ADD CONSTRAINT "departments_school_id_fkey" FOREIGN KEY ("school_id") REFERENCES "kingdom_sis"."schools"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kingdom_sis"."students" ADD CONSTRAINT "students_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "kingdom_sis"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kingdom_sis"."students" ADD CONSTRAINT "students_school_id_fkey" FOREIGN KEY ("school_id") REFERENCES "kingdom_sis"."schools"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kingdom_sis"."students" ADD CONSTRAINT "students_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "kingdom_sis"."departments"("id") ON DELETE SET NULL ON UPDATE CASCADE;
